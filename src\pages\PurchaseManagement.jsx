import React, { useState, useEffect } from 'react';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLanguage } from '../LanguageContext.jsx';

const PurchaseManagement = () => {
  const {
    currentUser,
    storeSettings,
    showToast
  } = useAppState();
  const { t, currentLanguage } = useLanguage();

  // Purchase management states
  const [editingPurchase, setEditingPurchase] = useState(null);
  const [purchaseInvoice, setPurchaseInvoice] = useState({
    invoiceNumber: '',
    date: '',
    supplierId: '',
    supplierName: '',
    paymentMethod: 'نقداً',
    items: [],
    total: 0,
    discount: 0,
    tax: 0,
    finalTotal: 0
  });
  const [suppliers, setSuppliers] = useState([]);
  const [savedPurchases, setSavedPurchases] = useState([]);
  const [products, setProducts] = useState([]);

  // Modal states
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showSupplierModal, setShowSupplierModal] = useState(false);
  const [showEditSupplierModal, setShowEditSupplierModal] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState(null);
  const [newSupplier, setNewSupplier] = useState({
    id: '',
    name: '',
    phone: '',
    address: '',
    email: ''
  });

  // Product selection states
  const [selectedProduct, setSelectedProduct] = useState('');
  const [productQuantity, setProductQuantity] = useState(1);
  const [productPrice, setProductPrice] = useState(0);

  // Filter and selection states
  const [selectedPurchases, setSelectedPurchases] = useState([]);
  const [purchaseInvoiceFilter, setPurchaseInvoiceFilter] = useState('');

  // Format price function
  const formatPrice = (price) => {
    if (price === null || price === undefined || isNaN(price)) return '0 dzd';
    const numPrice = parseFloat(price);
    if (numPrice === 0) return '0 dzd';

    // Format without decimals for clean display
    const formatted = Math.round(numPrice).toLocaleString('en-US');
    return `${formatted} dzd`;
  };

  // Load data from localStorage on component mount
  useEffect(() => {
    loadPurchases();
    loadSuppliers();
    loadProducts();
  }, []);

  // Load and save functions
  const loadPurchases = () => {
    const savedPurchases = localStorage.getItem('icaldz-purchases');
    if (savedPurchases) {
      setSavedPurchases(JSON.parse(savedPurchases));
    }
  };

  const savePurchases = (purchasesData) => {
    localStorage.setItem('icaldz-purchases', JSON.stringify(purchasesData));
    setSavedPurchases(purchasesData);
  };

  const loadSuppliers = () => {
    const savedSuppliers = localStorage.getItem('icaldz-suppliers');
    if (savedSuppliers) {
      setSuppliers(JSON.parse(savedSuppliers));
    }
  };

  const saveSuppliers = (suppliersData) => {
    localStorage.setItem('icaldz-suppliers', JSON.stringify(suppliersData));
    setSuppliers(suppliersData);
  };

  const loadProducts = () => {
    const savedProducts = localStorage.getItem('icaldz-products');
    if (savedProducts) {
      setProducts(JSON.parse(savedProducts));
    }
  };

  // Purchase modal functions
  const openPurchaseModal = () => {
    setShowPurchaseModal(true);
    const newInvoiceNumber = `PUR${String(savedPurchases.length + 1).padStart(4, '0')}`;
    setPurchaseInvoice({
      invoiceNumber: newInvoiceNumber,
      date: new Date().toISOString().split('T')[0],
      supplierId: '',
      supplierName: '',
      paymentMethod: 'نقداً',
      items: [],
      total: 0,
      discount: 0,
      tax: 0,
      finalTotal: 0
    });
    setEditingPurchase(null);
  };

  const closePurchaseModal = () => {
    setShowPurchaseModal(false);
    setPurchaseInvoice({
      invoiceNumber: '',
      date: '',
      supplierId: '',
      supplierName: '',
      paymentMethod: 'نقداً',
      items: [],
      total: 0,
      discount: 0,
      tax: 0,
      finalTotal: 0
    });
    setEditingPurchase(null);
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
  };

  const editPurchaseInvoice = (purchase) => {
    setEditingPurchase(purchase);
    setPurchaseInvoice({ ...purchase });
    setShowPurchaseModal(true);
  };

  const deletePurchaseInvoice = (purchaseId) => {
    if (window.confirm(t('confirmDeletePurchase', 'هل أنت متأكد من حذف هذه الفاتورة؟'))) {
      const updatedPurchases = savedPurchases.filter(purchase => purchase.id !== purchaseId);
      savePurchases(updatedPurchases);
      showToast(t('purchaseDeletedSuccessfully', 'تم حذف الفاتورة بنجاح'), 'success');
    }
  };

  const savePurchaseInvoice = () => {
    if (!purchaseInvoice.supplierName.trim()) {
      showToast(t('pleaseSelectSupplier', 'يرجى اختيار المورد'), 'error');
      return;
    }

    if (purchaseInvoice.items.length === 0) {
      showToast(t('pleaseAddItems', 'يرجى إضافة عناصر للفاتورة'), 'error');
      return;
    }

    const purchaseToSave = {
      ...purchaseInvoice,
      id: editingPurchase ? editingPurchase.id : Date.now().toString(),
      createdAt: editingPurchase ? editingPurchase.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: currentUser?.name || 'Admin'
    };

    let updatedPurchases;
    if (editingPurchase) {
      updatedPurchases = savedPurchases.map(purchase =>
        purchase.id === editingPurchase.id ? purchaseToSave : purchase
      );
      showToast(t('purchaseUpdatedSuccessfully', 'تم تحديث الفاتورة بنجاح'), 'success');
    } else {
      updatedPurchases = [...savedPurchases, purchaseToSave];
      showToast(t('purchaseAddedSuccessfully', 'تم إضافة الفاتورة بنجاح'), 'success');
    }

    // Update product stock
    purchaseInvoice.items.forEach(item => {
      const productIndex = products.findIndex(p => p.id === item.productId);
      if (productIndex !== -1) {
        const updatedProducts = [...products];
        updatedProducts[productIndex].stock = (updatedProducts[productIndex].stock || 0) + item.quantity;
        setProducts(updatedProducts);
        localStorage.setItem('icaldz-products', JSON.stringify(updatedProducts));
      }
    });

    savePurchases(updatedPurchases);
    closePurchaseModal();
  };

  // Supplier management functions
  const openSupplierModal = () => {
    setShowSupplierModal(true);
    setNewSupplier({
      id: `SUP${String(suppliers.length + 1).padStart(3, '0')}`,
      name: '',
      phone: '',
      address: '',
      email: ''
    });
  };

  const closeSupplierModal = () => {
    setShowSupplierModal(false);
    setNewSupplier({
      id: '',
      name: '',
      phone: '',
      address: '',
      email: ''
    });
  };

  const addSupplier = () => {
    if (!newSupplier.name.trim()) {
      showToast(t('pleaseEnterSupplierName', 'يرجى إدخال اسم المورد'), 'error');
      return;
    }

    // Check for duplicate supplier names (case-insensitive)
    const trimmedName = newSupplier.name.trim();
    const existingSupplierWithSameName = suppliers.find(s =>
      s.name.toLowerCase().trim() === trimmedName.toLowerCase() &&
      s.id !== newSupplier.id // Allow editing the same supplier
    );

    if (existingSupplierWithSameName) {
      showToast(`❌ ${t('supplierNameAlreadyExists', 'اسم المورد موجود بالفعل')}: "${trimmedName}" - ${t('pleaseChooseDifferentName', 'يرجى اختيار اسم مختلف')}`, 'error', 4000);
      return;
    }

    // Check for duplicate phone numbers (if provided)
    if (newSupplier.phone && newSupplier.phone.trim() !== '') {
      const existingSupplierWithSamePhone = suppliers.find(s =>
        s.phone === newSupplier.phone.trim() &&
        s.id !== newSupplier.id
      );

      if (existingSupplierWithSamePhone) {
        showToast(`❌ ${t('phoneNumberAlreadyExists', 'رقم الهاتف موجود بالفعل')}: "${newSupplier.phone}" - ${t('usedBySupplier', 'مستخدم بواسطة')}: "${existingSupplierWithSamePhone.name}"`, 'error', 4000);
        return;
      }
    }

    const supplierToAdd = {
      ...newSupplier,
      createdAt: new Date().toISOString()
    };

    const updatedSuppliers = [...suppliers, supplierToAdd];
    saveSuppliers(updatedSuppliers);

    closeSupplierModal();
    showToast(t('supplierAddedSuccessfully', 'تم إضافة المورد بنجاح'), 'success');
  };

  const deleteSupplier = (supplierId) => {
    if (window.confirm(t('confirmDeleteSupplier', 'هل أنت متأكد من حذف هذا المورد؟'))) {
      const updatedSuppliers = suppliers.filter(supplier => supplier.id !== supplierId);
      saveSuppliers(updatedSuppliers);
      showToast(t('supplierDeletedSuccessfully', 'تم حذف المورد بنجاح'), 'success');
    }
  };

  // Edit supplier functions
  const openEditSupplierModal = (supplier) => {
    setEditingSupplier({ ...supplier });
    setShowEditSupplierModal(true);
  };

  const closeEditSupplierModal = () => {
    setEditingSupplier(null);
    setShowEditSupplierModal(false);
  };

  const updateSupplier = () => {
    if (!editingSupplier.name.trim()) {
      showToast(t('pleaseEnterSupplierName', 'يرجى إدخال اسم المورد'), 'error');
      return;
    }

    const updatedSuppliers = suppliers.map(supplier =>
      supplier.id === editingSupplier.id ? editingSupplier : supplier
    );
    saveSuppliers(updatedSuppliers);

    closeEditSupplierModal();
    showToast(t('supplierUpdatedSuccessfully', 'تم تحديث المورد بنجاح'), 'success');
  };

  // Product management functions
  const addProductToPurchase = () => {
    if (!selectedProduct) {
      showToast(t('pleaseSelectProduct', 'يرجى اختيار المنتج'), 'error');
      return;
    }

    if (productQuantity <= 0) {
      showToast(t('pleaseEnterValidQuantity', 'يرجى إدخال كمية صحيحة'), 'error');
      return;
    }

    if (productPrice <= 0) {
      showToast(t('pleaseEnterValidPrice', 'يرجى إدخال سعر صحيح'), 'error');
      return;
    }

    const product = products.find(p => p.id === selectedProduct);
    if (!product) return;

    const existingItemIndex = purchaseInvoice.items.findIndex(item => item.productId === selectedProduct);

    let updatedItems;
    if (existingItemIndex !== -1) {
      // Update existing item
      updatedItems = [...purchaseInvoice.items];
      updatedItems[existingItemIndex].quantity += productQuantity;
      updatedItems[existingItemIndex].total = updatedItems[existingItemIndex].quantity * productPrice;
    } else {
      // Add new item
      const newItem = {
        productId: selectedProduct,
        productName: product.name,
        quantity: productQuantity,
        price: productPrice,
        total: productQuantity * productPrice
      };
      updatedItems = [...purchaseInvoice.items, newItem];
    }

    const total = updatedItems.reduce((sum, item) => sum + item.total, 0);
    const finalTotal = total - purchaseInvoice.discount + purchaseInvoice.tax;

    setPurchaseInvoice({
      ...purchaseInvoice,
      items: updatedItems,
      total: total,
      finalTotal: finalTotal
    });

    // Reset form
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
  };

  const removeProductFromPurchase = (productId) => {
    const updatedItems = purchaseInvoice.items.filter(item => item.productId !== productId);
    const total = updatedItems.reduce((sum, item) => sum + item.total, 0);
    const finalTotal = total - purchaseInvoice.discount + purchaseInvoice.tax;

    setPurchaseInvoice({
      ...purchaseInvoice,
      items: updatedItems,
      total: total,
      finalTotal: finalTotal
    });
  };

  // Calculate totals when discount or tax changes
  const updateTotals = (discount = purchaseInvoice.discount, tax = purchaseInvoice.tax) => {
    const total = purchaseInvoice.items.reduce((sum, item) => sum + item.total, 0);
    const finalTotal = total - discount + tax;

    setPurchaseInvoice({
      ...purchaseInvoice,
      discount: discount,
      tax: tax,
      total: total,
      finalTotal: finalTotal
    });
  };

  // Filter purchases
  const filteredPurchases = savedPurchases.filter(purchase =>
    purchase.invoiceNumber.toLowerCase().includes(purchaseInvoiceFilter.toLowerCase()) ||
    purchase.supplierName.toLowerCase().includes(purchaseInvoiceFilter.toLowerCase())
  );

  // Bulk operations
  const handleSelectAllPurchases = (checked) => {
    if (checked) {
      setSelectedPurchases(filteredPurchases.map(p => p.id));
    } else {
      setSelectedPurchases([]);
    }
  };

  const handleSelectPurchase = (purchaseId, checked) => {
    if (checked) {
      setSelectedPurchases([...selectedPurchases, purchaseId]);
    } else {
      setSelectedPurchases(selectedPurchases.filter(id => id !== purchaseId));
    }
  };

  const deleteSelectedPurchases = () => {
    if (selectedPurchases.length === 0) return;

    if (window.confirm(t('confirmDeleteSelectedPurchases', `هل أنت متأكد من حذف ${selectedPurchases.length} فاتورة؟`))) {
      const updatedPurchases = savedPurchases.filter(purchase => !selectedPurchases.includes(purchase.id));
      savePurchases(updatedPurchases);
      setSelectedPurchases([]);
      showToast(t('selectedPurchasesDeleted', 'تم حذف الفواتير المحددة'), 'success');
    }
  };

  return (
    <div className={`purchase-page ${currentLanguage === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Page Header */}
      <div className="page-header" style={{
        backgroundColor: '#498C8A',
        color: 'white',
        padding: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
      }}>
        <h1>📦 {t('purchaseManagement', 'إدارة المشتريات')}</h1>
        <div className="header-actions">
          <button
            className="btn btn-success"
            onClick={openPurchaseModal}
            style={{
              marginRight: currentLanguage === 'ar' ? '0' : '10px',
              marginLeft: currentLanguage === 'ar' ? '10px' : '0',
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}
          >
            ➕ {t('newPurchaseInvoice', 'فاتورة مشتريات جديدة')}
          </button>
          <button
            className="btn btn-info"
            onClick={openSupplierModal}
            style={{
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}
          >
            🏭 {t('addNewSupplier', 'إضافة مورد جديد')}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="page-content" style={{
        padding: '20px',
        minHeight: 'calc(100vh - 100px)',
        direction: currentLanguage === 'ar' ? 'rtl' : 'ltr'
      }}>
        {/* Purchase Invoices Section */}
        <div className="purchase-invoices-section" style={{
          backgroundColor: 'white',
          borderRadius: '10px',
          padding: '20px',
          marginBottom: '20px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}>
          <div className="section-header" style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px',
            borderBottom: '2px solid #498C8A',
            paddingBottom: '10px'
          }}>
            <h2 style={{
              color: '#498C8A',
              margin: 0,
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}>
              📋 {t('purchaseInvoices', 'فواتير المشتريات')}
            </h2>
            <div className="section-actions">
              <input
                type="text"
                placeholder={t('searchPurchases', 'البحث في الفواتير...')}
                value={purchaseInvoiceFilter}
                onChange={(e) => setPurchaseInvoiceFilter(e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '5px',
                  marginRight: currentLanguage === 'ar' ? '0' : '10px',
                  marginLeft: currentLanguage === 'ar' ? '10px' : '0',
                  fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                }}
              />
              {selectedPurchases.length > 0 && (
                <button
                  className="btn btn-danger"
                  onClick={deleteSelectedPurchases}
                  style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                  }}
                >
                  🗑️ {t('deleteSelected', 'حذف المحدد')} ({selectedPurchases.length})
                </button>
              )}
            </div>
          </div>

          {/* Purchase Invoices Table */}
          <div className="table-container" style={{
            overflowX: 'auto',
            maxHeight: '400px',
            border: '1px solid #ddd',
            borderRadius: '5px'
          }}>
            <table className={`data-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`} style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}>
              <thead style={{ backgroundColor: '#f8f9fa', position: 'sticky', top: 0 }}>
                <tr>
                  <th style={{ padding: '12px', border: '1px solid #ddd', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={selectedPurchases.length === filteredPurchases.length && filteredPurchases.length > 0}
                      onChange={(e) => handleSelectAllPurchases(e.target.checked)}
                    />
                  </th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('invoiceNumber', 'رقم الفاتورة')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('date', 'التاريخ')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('supplier', 'المورد')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('total', 'المجموع')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('paymentMethod', 'طريقة الدفع')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('actions', 'الإجراءات')}</th>
                </tr>
              </thead>
              <tbody>
                {filteredPurchases.length === 0 ? (
                  <tr>
                    <td colSpan="7" style={{
                      padding: '20px',
                      textAlign: 'center',
                      color: '#666',
                      fontStyle: 'italic'
                    }}>
                      {t('noPurchasesFound', 'لا توجد فواتير مشتريات')}
                    </td>
                  </tr>
                ) : (
                  filteredPurchases.map((purchase) => (
                    <tr key={purchase.id} style={{
                      backgroundColor: selectedPurchases.includes(purchase.id) ? '#e3f2fd' : 'white'
                    }}>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>
                        <input
                          type="checkbox"
                          checked={selectedPurchases.includes(purchase.id)}
                          onChange={(e) => handleSelectPurchase(purchase.id, e.target.checked)}
                        />
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>{purchase.invoiceNumber}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>
                        {new Date(purchase.date).toLocaleDateString()}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>{purchase.supplierName}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', fontWeight: 'bold', color: '#498C8A' }}>
                        {formatPrice(purchase.finalTotal)}
                      </td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>{purchase.paymentMethod}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>
                        <div style={{ display: 'flex', gap: '5px', justifyContent: 'center' }}>
                          <button
                            className="btn btn-primary btn-sm"
                            onClick={() => editPurchaseInvoice(purchase)}
                            title={t('editPurchase', 'تعديل الفاتورة')}
                          >
                            ✏️
                          </button>
                          <button
                            className="btn btn-danger btn-sm"
                            onClick={() => deletePurchaseInvoice(purchase.id)}
                            title={t('deletePurchase', 'حذف الفاتورة')}
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Suppliers Management Section */}
        <div className="suppliers-section" style={{
          backgroundColor: 'white',
          borderRadius: '10px',
          padding: '20px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}>
          <div className="section-header" style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px',
            borderBottom: '2px solid #498C8A',
            paddingBottom: '10px'
          }}>
            <h2 style={{
              color: '#498C8A',
              margin: 0,
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}>
              🏭 {t('suppliersManagement', 'إدارة الموردين')}
            </h2>
            <button
              className="btn btn-success"
              onClick={openSupplierModal}
              style={{
                fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
              }}
            >
              ➕ {t('addNewSupplierButton', 'إضافة مورد جديد')}
            </button>
          </div>

          <div className="suppliers-table-container" style={{
            direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
            overflowX: 'auto',
            maxHeight: '300px',
            border: '1px solid #ddd',
            borderRadius: '5px'
          }}>
            <table className={`data-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`} style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}>
              <thead style={{ backgroundColor: '#f8f9fa', position: 'sticky', top: 0 }}>
                <tr>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('supplierID', 'رقم المورد')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('supplierName', 'اسم المورد')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('supplierPhone', 'رقم الهاتف')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('supplierEmail', 'البريد الإلكتروني')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('supplierAddress', 'العنوان')}</th>
                  <th style={{ padding: '12px', border: '1px solid #ddd' }}>{t('actions', 'الإجراءات')}</th>
                </tr>
              </thead>
              <tbody>
                {suppliers.length === 0 ? (
                  <tr>
                    <td colSpan="6" style={{
                      padding: '20px',
                      textAlign: 'center',
                      color: '#666',
                      fontStyle: 'italic'
                    }}>
                      {t('noSuppliersFound', 'لا توجد موردين')}
                    </td>
                  </tr>
                ) : (
                  suppliers.map((supplier) => (
                    <tr key={supplier.id}>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>{supplier.id}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', fontWeight: 'bold' }}>{supplier.name}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>{supplier.phone || '-'}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>{supplier.email || '-'}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>{supplier.address || '-'}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd' }}>
                        <div style={{ display: 'flex', gap: '5px', justifyContent: 'center' }}>
                          <button
                            className="btn btn-primary btn-xs"
                            onClick={() => openEditSupplierModal(supplier)}
                            title={t('editSupplier', 'تعديل المورد')}
                            style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}
                          >
                            ✏️
                          </button>
                          <button
                            className="btn btn-danger btn-xs"
                            onClick={() => deleteSupplier(supplier.id)}
                            title={t('deleteSupplier', 'حذف المورد')}
                            style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Purchase Invoice Modal */}
      {showPurchaseModal && (
        <div className="modal-overlay" onClick={closePurchaseModal}>
          <div className={`modal-content purchase-modal-landscape lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="modal-title-section">
                <h2>📦 {editingPurchase ? t('editPurchaseInvoiceTitle', 'تعديل فاتورة المشتريات') : t('newPurchaseInvoice', 'فاتورة مشتريات جديدة')}</h2>
              </div>
              <button className="modal-close" onClick={closePurchaseModal}>×</button>
            </div>
            <div className="modal-body purchase-modal-body">
              {/* Top Section - Invoice Info */}
              <div className="invoice-info-section">
                <div className="form-row">
                  <div className="form-group">
                    <label>{t('invoiceNumber', 'رقم الفاتورة')}</label>
                    <input
                      type="text"
                      value={purchaseInvoice.invoiceNumber}
                      onChange={(e) => setPurchaseInvoice({...purchaseInvoice, invoiceNumber: e.target.value})}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>{t('date', 'التاريخ')}</label>
                    <input
                      type="date"
                      value={purchaseInvoice.date}
                      onChange={(e) => setPurchaseInvoice({...purchaseInvoice, date: e.target.value})}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>{t('supplier', 'المورد')}</label>
                    <select
                      value={purchaseInvoice.supplierId}
                      onChange={(e) => {
                        const supplierId = e.target.value;
                        if (supplierId === 'NEW_SUPPLIER') {
                          openSupplierModal();
                        } else if (supplierId === 'GENERAL') {
                          setPurchaseInvoice({
                            ...purchaseInvoice,
                            supplierId: 'GENERAL',
                            supplierName: t('generalSupplier', 'مورد عام')
                          });
                        } else {
                          const supplier = suppliers.find(s => s.id === supplierId);
                          if (supplier) {
                            setPurchaseInvoice({
                              ...purchaseInvoice,
                              supplierId: supplier.id,
                              supplierName: supplier.name
                            });
                          }
                        }
                      }}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    >
                      <option value="">{t('selectSupplier', 'اختر المورد')}</option>
                      <option value="GENERAL">{t('generalSupplier', 'مورد عام')}</option>
                      {suppliers.map(supplier => (
                        <option key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </option>
                      ))}
                      <option value="NEW_SUPPLIER">{t('addNewSupplier', '+ إضافة مورد جديد')}</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>{t('paymentMethod', 'طريقة الدفع')}</label>
                    <select
                      value={purchaseInvoice.paymentMethod}
                      onChange={(e) => setPurchaseInvoice({...purchaseInvoice, paymentMethod: e.target.value})}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    >
                      <option value="نقداً">{t('cash', 'نقداً')}</option>
                      <option value="شيك">{t('check', 'شيك')}</option>
                      <option value="تحويل بنكي">{t('bankTransfer', 'تحويل بنكي')}</option>
                      <option value="بطاقة ائتمان">{t('creditCard', 'بطاقة ائتمان')}</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Product Selection Section */}
              <div className="product-selection-section">
                <h3>{t('addProducts', 'إضافة المنتجات')}</h3>
                <div className="form-row">
                  <div className="form-group">
                    <label>{t('product', 'المنتج')}</label>
                    <select
                      value={selectedProduct}
                      onChange={(e) => {
                        setSelectedProduct(e.target.value);
                        const product = products.find(p => p.id === e.target.value);
                        if (product) {
                          setProductPrice(product.price || 0);
                        }
                      }}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    >
                      <option value="">{t('selectProduct', 'اختر المنتج')}</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name} - {formatPrice(product.price)}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="form-group">
                    <label>{t('quantity', 'الكمية')}</label>
                    <input
                      type="number"
                      min="1"
                      value={productQuantity}
                      onChange={(e) => setProductQuantity(parseInt(e.target.value) || 1)}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>{t('price', 'السعر')}</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={productPrice}
                      onChange={(e) => setProductPrice(parseFloat(e.target.value) || 0)}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <button
                      type="button"
                      className="btn btn-primary"
                      onClick={addProductToPurchase}
                      style={{
                        marginTop: '25px',
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    >
                      ➕ {t('addProduct', 'إضافة المنتج')}
                    </button>
                  </div>
                </div>
              </div>

              {/* Items Table */}
              <div className="items-section">
                <h3>{t('invoiceItems', 'عناصر الفاتورة')}</h3>
                <div className="items-table-container">
                  <table className="items-table">
                    <thead>
                      <tr>
                        <th>{t('product', 'المنتج')}</th>
                        <th>{t('quantity', 'الكمية')}</th>
                        <th>{t('price', 'السعر')}</th>
                        <th>{t('total', 'المجموع')}</th>
                        <th>{t('actions', 'الإجراءات')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {purchaseInvoice.items.length === 0 ? (
                        <tr>
                          <td colSpan="5" style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
                            {t('noItemsAdded', 'لم يتم إضافة عناصر بعد')}
                          </td>
                        </tr>
                      ) : (
                        purchaseInvoice.items.map((item, index) => (
                          <tr key={index}>
                            <td>{item.productName}</td>
                            <td>{item.quantity}</td>
                            <td>{formatPrice(item.price)}</td>
                            <td style={{ fontWeight: 'bold', color: '#498C8A' }}>{formatPrice(item.total)}</td>
                            <td>
                              <button
                                className="btn btn-danger btn-sm"
                                onClick={() => removeProductFromPurchase(item.productId)}
                                title={t('removeItem', 'إزالة العنصر')}
                              >
                                🗑️
                              </button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Totals Section */}
              <div className="totals-section">
                <div className="form-row">
                  <div className="form-group">
                    <label>{t('discount', 'الخصم')}</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={purchaseInvoice.discount}
                      onChange={(e) => updateTotals(parseFloat(e.target.value) || 0, purchaseInvoice.tax)}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>{t('tax', 'الضريبة')}</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={purchaseInvoice.tax}
                      onChange={(e) => updateTotals(purchaseInvoice.discount, parseFloat(e.target.value) || 0)}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>{t('subtotal', 'المجموع الفرعي')}</label>
                    <input
                      type="text"
                      value={formatPrice(purchaseInvoice.total)}
                      readOnly
                      style={{
                        backgroundColor: '#f8f9fa',
                        fontWeight: 'bold',
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                  <div className="form-group">
                    <label>{t('finalTotal', 'المجموع النهائي')}</label>
                    <input
                      type="text"
                      value={formatPrice(purchaseInvoice.finalTotal)}
                      readOnly
                      style={{
                        backgroundColor: '#e8f5e8',
                        fontWeight: 'bold',
                        color: '#498C8A',
                        fontSize: '1.1em',
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className={`modal-footer ${currentLanguage !== 'ar' ? 'modal-footer-ltr' : ''}`}>
              <button className="btn btn-success" onClick={savePurchaseInvoice}>
                ✅ {editingPurchase ? t('updatePurchase', 'تحديث الفاتورة') : t('savePurchase', 'حفظ الفاتورة')}
              </button>
              <button className="btn btn-secondary" onClick={closePurchaseModal}>
                ❌ {t('cancel', 'إلغاء')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Supplier Modal */}
      {showSupplierModal && (
        <div className="modal-overlay" onClick={closeSupplierModal}>
          <div className={`modal-content supplier-modal ${currentLanguage !== 'ar' ? 'modal-ltr' : ''}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <h2>🏭 {t('addNewSupplier', 'إضافة مورّد جديد')}</h2>
              <button className="modal-close" onClick={closeSupplierModal}>×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>{t('supplierNumber', 'رقم المورد')}</label>
                  <input type="text" value={newSupplier.id} readOnly />
                </div>
                <div className="form-group">
                  <label>{t('supplierName', 'اسم المورد')} *</label>
                  <input
                    type="text"
                    value={newSupplier.name}
                    onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                    placeholder={t('enterSupplierName', 'أدخل اسم المورد')}
                    required
                    style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                    }}
                  />
                </div>
                <div className="form-group">
                  <label>{t('supplierPhone', 'رقم الهاتف')}</label>
                  <input
                    type="tel"
                    value={newSupplier.phone}
                    onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                    placeholder={t('phoneNumberPlaceholder', 'رقم الهاتف')}
                    style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                    }}
                  />
                </div>
                <div className="form-group">
                  <label>{t('supplierEmail', 'البريد الإلكتروني')}</label>
                  <input
                    type="email"
                    value={newSupplier.email}
                    onChange={(e) => setNewSupplier({...newSupplier, email: e.target.value})}
                    placeholder={t('emailPlaceholder', 'البريد الإلكتروني')}
                    style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                    }}
                  />
                </div>
                <div className="form-group full-width">
                  <label>{t('supplierAddress', 'العنوان')}</label>
                  <input
                    type="text"
                    value={newSupplier.address}
                    onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                    placeholder={t('fullAddress', 'العنوان الكامل')}
                    style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                    }}
                  />
                </div>
              </div>
            </div>
            <div className={`modal-footer ${currentLanguage !== 'ar' ? 'modal-footer-ltr' : ''}`}>
              <button className="btn btn-success" onClick={addSupplier}>
                ✅ {t('saveSupplier', 'حفظ المورد')}
              </button>
              <button className="btn btn-secondary" onClick={closeSupplierModal}>
                ❌ {t('cancel', 'إلغاء')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Supplier Modal */}
      {showEditSupplierModal && editingSupplier && (
        <div className="modal-overlay" onClick={closeEditSupplierModal}>
          <div className={`modal-content supplier-modal ${currentLanguage !== 'ar' ? 'modal-ltr' : ''}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <h2>✏️ {t('editSupplier', 'تعديل المورد')}</h2>
              <button className="modal-close" onClick={closeEditSupplierModal}>×</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>{t('supplierNumber', 'رقم المورد')}</label>
                  <input type="text" value={editingSupplier.id} readOnly />
                </div>
                <div className="form-group">
                  <label>{t('supplierName', 'اسم المورد')} *</label>
                  <input
                    type="text"
                    value={editingSupplier.name}
                    onChange={(e) => setEditingSupplier({...editingSupplier, name: e.target.value})}
                    placeholder={t('enterSupplierName', 'أدخل اسم المورد')}
                    autoFocus
                    style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                    }}
                  />
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>{t('supplierPhone', 'رقم الهاتف')}</label>
                  <input
                    type="tel"
                    value={editingSupplier.phone}
                    onChange={(e) => setEditingSupplier({...editingSupplier, phone: e.target.value})}
                    placeholder={t('enterSupplierPhone', 'أدخل رقم الهاتف')}
                    style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                    }}
                  />
                </div>
                <div className="form-group">
                  <label>{t('supplierEmail', 'البريد الإلكتروني')}</label>
                  <input
                    type="email"
                    value={editingSupplier.email}
                    onChange={(e) => setEditingSupplier({...editingSupplier, email: e.target.value})}
                    placeholder={t('enterSupplierEmail', 'أدخل البريد الإلكتروني')}
                    style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                    }}
                  />
                </div>
              </div>
              <div className="form-group">
                <label>{t('supplierAddress', 'العنوان')}</label>
                <textarea
                  value={editingSupplier.address}
                  onChange={(e) => setEditingSupplier({...editingSupplier, address: e.target.value})}
                  placeholder={t('enterSupplierAddress', 'أدخل العنوان')}
                  rows="3"
                  style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                  }}
                />
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeEditSupplierModal}>
                {t('cancel', 'إلغاء')}
              </button>
              <button className="btn btn-primary" onClick={updateSupplier}>
                {t('save', 'حفظ')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PurchaseManagement;
