{"version": 3, "names": ["_t", "require", "_index", "isArrayTypeAnnotation", "isBinaryExpression", "isCallExpression", "isForOfStatement", "isIndexedAccessType", "isMemberExpression", "isObjectPattern", "isOptionalMemberExpression", "isYieldExpression", "isStatement", "PRECEDENCE", "Map", "getBinaryPrecedence", "node", "nodeType", "get", "operator", "isTSTypeExpression", "isClassExtendsClause", "parent", "parentType", "type", "superClass", "hasPostfixPart", "object", "callee", "tag", "NullableTypeAnnotation", "FunctionTypeAnnotation", "tokenContext", "Boolean", "TokenContext", "arrowFlowReturnType", "UpdateExpression", "needsParenBeforeExpressionBrace", "expressionStatement", "arrowBody", "ObjectExpression", "DoExpression", "async", "Binary", "left", "parentPos", "nodePos", "right", "undefined", "UnionTypeAnnotation", "OptionalIndexedAccessType", "objectType", "TSAsExpression", "TSConditionalType", "types", "checkType", "extendsType", "TSUnionType", "TSIntersectionType", "TSInferType", "typeParameter", "constraint", "TSTypeOperator", "TSInstantiationExpression", "typeParameters", "TSFunctionType", "BinaryExpression", "inForStatementInit", "SequenceExpression", "property", "YieldExpression", "test", "ClassExpression", "exportDefault", "UnaryLike", "FunctionExpression", "ConditionalExpression", "OptionalMemberExpression", "AssignmentExpression", "LogicalExpression", "Identifier", "_inForInit", "getRawIdentifier", "_node$extra", "extra", "parenthesized", "rightType", "id", "name", "isFollowedByBracket", "computed", "optional", "forHead", "forInHead", "forOfHead", "await"], "sources": ["../../src/node/parentheses.ts"], "sourcesContent": ["import {\n  isArrayTypeAnnotation,\n  isBinaryExpression,\n  isCallExpression,\n  isForOfStatement,\n  isIndexedAccessType,\n  isMemberExpression,\n  isObjectPattern,\n  isOptionalMemberExpression,\n  isYieldExpression,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nimport { TokenContext } from \"./index.ts\";\n\nconst PRECEDENCE = new Map([\n  [\"||\", 0],\n  [\"??\", 0],\n  [\"|>\", 0],\n  [\"&&\", 1],\n  [\"|\", 2],\n  [\"^\", 3],\n  [\"&\", 4],\n  [\"==\", 5],\n  [\"===\", 5],\n  [\"!=\", 5],\n  [\"!==\", 5],\n  [\"<\", 6],\n  [\">\", 6],\n  [\"<=\", 6],\n  [\">=\", 6],\n  [\"in\", 6],\n  [\"instanceof\", 6],\n  [\">>\", 7],\n  [\"<<\", 7],\n  [\">>>\", 7],\n  [\"+\", 8],\n  [\"-\", 8],\n  [\"*\", 9],\n  [\"/\", 9],\n  [\"%\", 9],\n  [\"**\", 10],\n]);\n\nfunction getBinaryPrecedence(\n  node: t.Binary | t.TSAsExpression | t.TSSatisfiesExpression,\n  nodeType: string,\n): number;\nfunction getBinaryPrecedence(\n  node: t.Node,\n  nodeType: string,\n): number | undefined;\nfunction getBinaryPrecedence(node: t.Node, nodeType: string) {\n  if (nodeType === \"BinaryExpression\" || nodeType === \"LogicalExpression\") {\n    return PRECEDENCE.get((node as t.Binary).operator);\n  }\n  if (nodeType === \"TSAsExpression\" || nodeType === \"TSSatisfiesExpression\") {\n    return PRECEDENCE.get(\"in\");\n  }\n}\n\nfunction isTSTypeExpression(nodeType: string) {\n  return (\n    nodeType === \"TSAsExpression\" ||\n    nodeType === \"TSSatisfiesExpression\" ||\n    nodeType === \"TSTypeAssertion\"\n  );\n}\n\nconst isClassExtendsClause = (\n  node: t.Node,\n  parent: t.Node,\n): parent is t.Class => {\n  const parentType = parent.type;\n  return (\n    (parentType === \"ClassDeclaration\" || parentType === \"ClassExpression\") &&\n    parent.superClass === node\n  );\n};\n\nconst hasPostfixPart = (node: t.Node, parent: t.Node) => {\n  const parentType = parent.type;\n  return (\n    ((parentType === \"MemberExpression\" ||\n      parentType === \"OptionalMemberExpression\") &&\n      parent.object === node) ||\n    ((parentType === \"CallExpression\" ||\n      parentType === \"OptionalCallExpression\" ||\n      parentType === \"NewExpression\") &&\n      parent.callee === node) ||\n    (parentType === \"TaggedTemplateExpression\" && parent.tag === node) ||\n    parentType === \"TSNonNullExpression\"\n  );\n};\n\nexport function NullableTypeAnnotation(\n  node: t.NullableTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  return isArrayTypeAnnotation(parent);\n}\n\nexport function FunctionTypeAnnotation(\n  node: t.FunctionTypeAnnotation,\n  parent: t.Node,\n  tokenContext: number,\n): boolean {\n  const parentType = parent.type;\n  return (\n    // (() => A) | (() => B)\n    parentType === \"UnionTypeAnnotation\" ||\n    // (() => A) & (() => B)\n    parentType === \"IntersectionTypeAnnotation\" ||\n    // (() => A)[]\n    parentType === \"ArrayTypeAnnotation\" ||\n    Boolean(tokenContext & TokenContext.arrowFlowReturnType)\n  );\n}\n\nexport function UpdateExpression(\n  node: t.UpdateExpression,\n  parent: t.Node,\n): boolean {\n  return hasPostfixPart(node, parent) || isClassExtendsClause(node, parent);\n}\n\nfunction needsParenBeforeExpressionBrace(tokenContext: number) {\n  return Boolean(\n    tokenContext & (TokenContext.expressionStatement | TokenContext.arrowBody),\n  );\n}\n\nexport function ObjectExpression(\n  node: t.ObjectExpression,\n  parent: t.Node,\n  tokenContext: number,\n): boolean {\n  return needsParenBeforeExpressionBrace(tokenContext);\n}\n\nexport function DoExpression(\n  node: t.DoExpression,\n  parent: t.Node,\n  tokenContext: number,\n): boolean {\n  // `async do` can start an expression statement\n  return (\n    !node.async && Boolean(tokenContext & TokenContext.expressionStatement)\n  );\n}\n\nexport function Binary(\n  node: t.Binary | t.TSAsExpression | t.TSSatisfiesExpression,\n  parent: t.Node,\n): boolean | undefined {\n  const parentType = parent.type;\n  if (\n    node.type === \"BinaryExpression\" &&\n    node.operator === \"**\" &&\n    parentType === \"BinaryExpression\" &&\n    parent.operator === \"**\"\n  ) {\n    return parent.left === node;\n  }\n\n  if (isClassExtendsClause(node, parent)) {\n    return true;\n  }\n\n  if (\n    hasPostfixPart(node, parent) ||\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    parentType === \"AwaitExpression\"\n  ) {\n    return true;\n  }\n\n  const parentPos = getBinaryPrecedence(parent, parentType);\n  if (parentPos != null) {\n    const nodePos = getBinaryPrecedence(node, node.type);\n    if (\n      // Logical expressions with the same precedence don't need parens.\n      (parentPos === nodePos &&\n        parentType === \"BinaryExpression\" &&\n        parent.right === node) ||\n      parentPos > nodePos\n    ) {\n      return true;\n    }\n  }\n\n  return undefined;\n}\n\nexport function UnionTypeAnnotation(\n  node: t.UnionTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"ArrayTypeAnnotation\" ||\n    parentType === \"NullableTypeAnnotation\" ||\n    parentType === \"IntersectionTypeAnnotation\" ||\n    parentType === \"UnionTypeAnnotation\"\n  );\n}\n\nexport { UnionTypeAnnotation as IntersectionTypeAnnotation };\n\nexport function OptionalIndexedAccessType(\n  node: t.OptionalIndexedAccessType,\n  parent: t.Node,\n): boolean {\n  return isIndexedAccessType(parent) && parent.objectType === node;\n}\n\nexport function TSAsExpression(\n  node: t.TSAsExpression | t.TSSatisfiesExpression,\n  parent: t.Node,\n): boolean {\n  if (\n    (parent.type === \"AssignmentExpression\" ||\n      parent.type === \"AssignmentPattern\") &&\n    parent.left === node\n  ) {\n    return true;\n  }\n  if (\n    parent.type === \"BinaryExpression\" &&\n    (parent.operator === \"|\" || parent.operator === \"&\") &&\n    node === parent.left\n  ) {\n    return true;\n  }\n  return Binary(node, parent);\n}\n\nexport { TSAsExpression as TSSatisfiesExpression };\n\nexport { UnaryLike as TSTypeAssertion };\n\nexport function TSConditionalType(\n  node: t.TSConditionalType,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (\n    parentType === \"TSArrayType\" ||\n    (parentType === \"TSIndexedAccessType\" && parent.objectType === node) ||\n    parentType === \"TSOptionalType\" ||\n    parentType === \"TSTypeOperator\" ||\n    // for `infer K extends (L extends M ? M : ...)`\n    parentType === \"TSTypeParameter\"\n  ) {\n    return true;\n  }\n  if (\n    (parentType === \"TSIntersectionType\" || parentType === \"TSUnionType\") &&\n    parent.types[0] === node\n  ) {\n    return true;\n  }\n  if (\n    parentType === \"TSConditionalType\" &&\n    (parent.checkType === node || parent.extendsType === node)\n  ) {\n    return true;\n  }\n  return false;\n}\n\nexport function TSUnionType(node: t.TSUnionType, parent: t.Node): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"TSIntersectionType\" ||\n    parentType === \"TSTypeOperator\" ||\n    parentType === \"TSArrayType\" ||\n    (parentType === \"TSIndexedAccessType\" && parent.objectType === node) ||\n    parentType === \"TSOptionalType\"\n  );\n}\n\nexport function TSIntersectionType(\n  node: t.TSUnionType,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"TSTypeOperator\" ||\n    parentType === \"TSArrayType\" ||\n    (parentType === \"TSIndexedAccessType\" && parent.objectType === node) ||\n    parentType === \"TSOptionalType\"\n  );\n}\n\nexport function TSInferType(node: t.TSInferType, parent: t.Node): boolean {\n  const parentType = parent.type;\n  if (\n    parentType === \"TSArrayType\" ||\n    (parentType === \"TSIndexedAccessType\" && parent.objectType === node) ||\n    parentType === \"TSOptionalType\"\n  ) {\n    return true;\n  }\n  if (node.typeParameter.constraint) {\n    if (\n      (parentType === \"TSIntersectionType\" || parentType === \"TSUnionType\") &&\n      parent.types[0] === node\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport function TSTypeOperator(\n  node: t.TSTypeOperator,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"TSArrayType\" ||\n    (parentType === \"TSIndexedAccessType\" && parent.objectType === node) ||\n    parentType === \"TSOptionalType\"\n  );\n}\n\nexport function TSInstantiationExpression(\n  node: t.TSInstantiationExpression,\n  parent: t.Node,\n) {\n  const parentType = parent.type;\n  return (\n    (parentType === \"CallExpression\" ||\n      parentType === \"OptionalCallExpression\" ||\n      parentType === \"NewExpression\" ||\n      parentType === \"TSInstantiationExpression\") &&\n    !!(process.env.BABEL_8_BREAKING\n      ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n        parent.typeArguments\n      : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n        parent.typeParameters)\n  );\n}\n\nexport function TSFunctionType(\n  node: t.TSFunctionType,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"TSIntersectionType\" ||\n    parentType === \"TSUnionType\" ||\n    parentType === \"TSTypeOperator\" ||\n    parentType === \"TSOptionalType\" ||\n    parentType === \"TSArrayType\" ||\n    (parentType === \"TSIndexedAccessType\" && parent.objectType === node) ||\n    (parentType === \"TSConditionalType\" &&\n      (parent.checkType === node || parent.extendsType === node))\n  );\n}\n\nexport { TSFunctionType as TSConstructorType };\n\nexport function BinaryExpression(\n  node: t.BinaryExpression,\n  parent: t.Node,\n  tokenContext: unknown,\n  inForStatementInit: boolean,\n): boolean {\n  // for ((1 in []);;);\n  // for (var x = (1 in []) in 2);\n  return node.operator === \"in\" && inForStatementInit;\n}\n\nexport function SequenceExpression(\n  node: t.SequenceExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (\n    parentType === \"SequenceExpression\" ||\n    parentType === \"ParenthesizedExpression\" ||\n    (parentType === \"MemberExpression\" && parent.property === node) ||\n    (parentType === \"OptionalMemberExpression\" && parent.property === node) ||\n    parentType === \"TemplateLiteral\"\n  ) {\n    return false;\n  }\n  if (parentType === \"ClassDeclaration\") {\n    return true;\n  }\n  if (parentType === \"ForOfStatement\") {\n    return parent.right === node;\n  }\n  if (parentType === \"ExportDefaultDeclaration\") {\n    return true;\n  }\n\n  return !isStatement(parent);\n}\n\nexport function YieldExpression(\n  node: t.YieldExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"BinaryExpression\" ||\n    parentType === \"LogicalExpression\" ||\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    hasPostfixPart(node, parent) ||\n    (parentType === \"AwaitExpression\" && isYieldExpression(node)) ||\n    (parentType === \"ConditionalExpression\" && node === parent.test) ||\n    isClassExtendsClause(node, parent) ||\n    isTSTypeExpression(parentType)\n  );\n}\n\nexport { YieldExpression as AwaitExpression };\n\nexport function ClassExpression(\n  node: t.ClassExpression,\n  parent: t.Node,\n  tokenContext: number,\n): boolean {\n  return Boolean(\n    tokenContext &\n      (TokenContext.expressionStatement | TokenContext.exportDefault),\n  );\n}\n\nexport function UnaryLike(\n  node:\n    | t.UnaryLike\n    | t.TSTypeAssertion\n    | t.ArrowFunctionExpression\n    | t.ConditionalExpression\n    | t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    hasPostfixPart(node, parent) ||\n    (isBinaryExpression(parent) &&\n      parent.operator === \"**\" &&\n      parent.left === node) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport function FunctionExpression(\n  node: t.FunctionExpression,\n  parent: t.Node,\n  tokenContext: number,\n): boolean {\n  return Boolean(\n    tokenContext &\n      (TokenContext.expressionStatement | TokenContext.exportDefault),\n  );\n}\n\nexport function ConditionalExpression(\n  node:\n    | t.ConditionalExpression\n    | t.ArrowFunctionExpression\n    | t.AssignmentExpression,\n  parent?: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    parentType === \"BinaryExpression\" ||\n    parentType === \"LogicalExpression\" ||\n    (parentType === \"ConditionalExpression\" && parent.test === node) ||\n    parentType === \"AwaitExpression\" ||\n    isTSTypeExpression(parentType)\n  ) {\n    return true;\n  }\n\n  return UnaryLike(node, parent);\n}\n\nexport { ConditionalExpression as ArrowFunctionExpression };\n\nexport function OptionalMemberExpression(\n  node: t.OptionalMemberExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    (isCallExpression(parent) && parent.callee === node) ||\n    (isMemberExpression(parent) && parent.object === node)\n  );\n}\n\nexport { OptionalMemberExpression as OptionalCallExpression };\n\nexport function AssignmentExpression(\n  node: t.AssignmentExpression,\n  parent: t.Node,\n  tokenContext: number,\n): boolean {\n  if (\n    needsParenBeforeExpressionBrace(tokenContext) &&\n    isObjectPattern(node.left)\n  ) {\n    return true;\n  } else {\n    return ConditionalExpression(node, parent);\n  }\n}\n\nexport function LogicalExpression(\n  node: t.LogicalExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (isTSTypeExpression(parentType)) return true;\n  if (parentType !== \"LogicalExpression\") return false;\n  switch (node.operator) {\n    case \"||\":\n      return parent.operator === \"??\" || parent.operator === \"&&\";\n    case \"&&\":\n      return parent.operator === \"??\";\n    case \"??\":\n      return parent.operator !== \"??\";\n  }\n}\n\nexport function Identifier(\n  node: t.Identifier,\n  parent: t.Node,\n  tokenContext: number,\n  _inForInit: boolean,\n  getRawIdentifier: (node: t.Identifier) => string,\n): boolean {\n  const parentType = parent.type;\n  // 13.15.2 AssignmentExpression RS: Evaluation\n  // (fn) = function () {};\n  if (\n    node.extra?.parenthesized &&\n    parentType === \"AssignmentExpression\" &&\n    parent.left === node\n  ) {\n    const rightType = parent.right.type;\n    if (\n      (rightType === \"FunctionExpression\" || rightType === \"ClassExpression\") &&\n      parent.right.id == null\n    ) {\n      return true;\n    }\n  }\n\n  if (getRawIdentifier && getRawIdentifier(node) !== node.name) {\n    return false;\n  }\n\n  // Non-strict code allows the identifier `let`, but it cannot occur as-is in\n  // certain contexts to avoid ambiguity with contextual keyword `let`.\n  if (node.name === \"let\") {\n    // Some contexts only forbid `let [`, so check if the next token would\n    // be the left bracket of a computed member expression.\n    const isFollowedByBracket =\n      isMemberExpression(parent, {\n        object: node,\n        computed: true,\n      }) ||\n      isOptionalMemberExpression(parent, {\n        object: node,\n        computed: true,\n        optional: false,\n      });\n    if (\n      isFollowedByBracket &&\n      tokenContext &\n        (TokenContext.expressionStatement |\n          TokenContext.forHead |\n          TokenContext.forInHead)\n    ) {\n      return true;\n    }\n    return Boolean(tokenContext & TokenContext.forOfHead);\n  }\n\n  // ECMAScript specifically forbids a for-of loop from starting with the\n  // token sequence `for (async of`, because it would be ambiguous with\n  // `for (async of => {};;)`, so we need to add extra parentheses.\n  return (\n    node.name === \"async\" &&\n    isForOfStatement(parent, { left: node, await: false })\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAcA,IAAAC,MAAA,GAAAD,OAAA;AAA0C;EAbxCE,qBAAqB;EACrBC,kBAAkB;EAClBC,gBAAgB;EAChBC,gBAAgB;EAChBC,mBAAmB;EACnBC,kBAAkB;EAClBC,eAAe;EACfC,0BAA0B;EAC1BC,iBAAiB;EACjBC;AAAW,IAAAZ,EAAA;AAMb,MAAMa,UAAU,GAAG,IAAIC,GAAG,CAAC,CACzB,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,YAAY,EAAE,CAAC,CAAC,EACjB,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,EAAE,CAAC,CACX,CAAC;AAUF,SAASC,mBAAmBA,CAACC,IAAY,EAAEC,QAAgB,EAAE;EAC3D,IAAIA,QAAQ,KAAK,kBAAkB,IAAIA,QAAQ,KAAK,mBAAmB,EAAE;IACvE,OAAOJ,UAAU,CAACK,GAAG,CAAEF,IAAI,CAAcG,QAAQ,CAAC;EACpD;EACA,IAAIF,QAAQ,KAAK,gBAAgB,IAAIA,QAAQ,KAAK,uBAAuB,EAAE;IACzE,OAAOJ,UAAU,CAACK,GAAG,CAAC,IAAI,CAAC;EAC7B;AACF;AAEA,SAASE,kBAAkBA,CAACH,QAAgB,EAAE;EAC5C,OACEA,QAAQ,KAAK,gBAAgB,IAC7BA,QAAQ,KAAK,uBAAuB,IACpCA,QAAQ,KAAK,iBAAiB;AAElC;AAEA,MAAMI,oBAAoB,GAAGA,CAC3BL,IAAY,EACZM,MAAc,KACQ;EACtB,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACE,CAACD,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,iBAAiB,KACtED,MAAM,CAACG,UAAU,KAAKT,IAAI;AAE9B,CAAC;AAED,MAAMU,cAAc,GAAGA,CAACV,IAAY,EAAEM,MAAc,KAAK;EACvD,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACG,CAACD,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,0BAA0B,KACzCD,MAAM,CAACK,MAAM,KAAKX,IAAI,IACvB,CAACO,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,eAAe,KAC9BD,MAAM,CAACM,MAAM,KAAKZ,IAAK,IACxBO,UAAU,KAAK,0BAA0B,IAAID,MAAM,CAACO,GAAG,KAAKb,IAAK,IAClEO,UAAU,KAAK,qBAAqB;AAExC,CAAC;AAEM,SAASO,sBAAsBA,CACpCd,IAA8B,EAC9BM,MAAc,EACL;EACT,OAAOnB,qBAAqB,CAACmB,MAAM,CAAC;AACtC;AAEO,SAASS,sBAAsBA,CACpCf,IAA8B,EAC9BM,MAAc,EACdU,YAAoB,EACX;EACT,MAAMT,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,QAEED,UAAU,KAAK,qBAAqB,IAEpCA,UAAU,KAAK,4BAA4B,IAE3CA,UAAU,KAAK,qBAAqB,IACpCU,OAAO,CAACD,YAAY,GAAGE,mBAAY,CAACC,mBAAmB;EAAC;AAE5D;AAEO,SAASC,gBAAgBA,CAC9BpB,IAAwB,EACxBM,MAAc,EACL;EACT,OAAOI,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAAID,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC;AAC3E;AAEA,SAASe,+BAA+BA,CAACL,YAAoB,EAAE;EAC7D,OAAOC,OAAO,CACZD,YAAY,IAAIE,mBAAY,CAACI,mBAAmB,GAAGJ,mBAAY,CAACK,SAAS,CAC3E,CAAC;AACH;AAEO,SAASC,gBAAgBA,CAC9BxB,IAAwB,EACxBM,MAAc,EACdU,YAAoB,EACX;EACT,OAAOK,+BAA+B,CAACL,YAAY,CAAC;AACtD;AAEO,SAASS,YAAYA,CAC1BzB,IAAoB,EACpBM,MAAc,EACdU,YAAoB,EACX;EAET,OACE,CAAChB,IAAI,CAAC0B,KAAK,IAAIT,OAAO,CAACD,YAAY,GAAGE,mBAAY,CAACI,mBAAmB,CAAC;AAE3E;AAEO,SAASK,MAAMA,CACpB3B,IAA2D,EAC3DM,MAAc,EACO;EACrB,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACER,IAAI,CAACQ,IAAI,KAAK,kBAAkB,IAChCR,IAAI,CAACG,QAAQ,KAAK,IAAI,IACtBI,UAAU,KAAK,kBAAkB,IACjCD,MAAM,CAACH,QAAQ,KAAK,IAAI,EACxB;IACA,OAAOG,MAAM,CAACsB,IAAI,KAAK5B,IAAI;EAC7B;EAEA,IAAIK,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC,EAAE;IACtC,OAAO,IAAI;EACb;EAEA,IACEI,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAC5BC,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,iBAAiB,EAChC;IACA,OAAO,IAAI;EACb;EAEA,MAAMsB,SAAS,GAAG9B,mBAAmB,CAACO,MAAM,EAAEC,UAAU,CAAC;EACzD,IAAIsB,SAAS,IAAI,IAAI,EAAE;IACrB,MAAMC,OAAO,GAAG/B,mBAAmB,CAACC,IAAI,EAAEA,IAAI,CAACQ,IAAI,CAAC;IACpD,IAEGqB,SAAS,KAAKC,OAAO,IACpBvB,UAAU,KAAK,kBAAkB,IACjCD,MAAM,CAACyB,KAAK,KAAK/B,IAAI,IACvB6B,SAAS,GAAGC,OAAO,EACnB;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAOE,SAAS;AAClB;AAEO,SAASC,mBAAmBA,CACjCjC,IAA2B,EAC3BM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,qBAAqB,IACpCA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,4BAA4B,IAC3CA,UAAU,KAAK,qBAAqB;AAExC;AAIO,SAAS2B,yBAAyBA,CACvClC,IAAiC,EACjCM,MAAc,EACL;EACT,OAAOf,mBAAmB,CAACe,MAAM,CAAC,IAAIA,MAAM,CAAC6B,UAAU,KAAKnC,IAAI;AAClE;AAEO,SAASoC,cAAcA,CAC5BpC,IAAgD,EAChDM,MAAc,EACL;EACT,IACE,CAACA,MAAM,CAACE,IAAI,KAAK,sBAAsB,IACrCF,MAAM,CAACE,IAAI,KAAK,mBAAmB,KACrCF,MAAM,CAACsB,IAAI,KAAK5B,IAAI,EACpB;IACA,OAAO,IAAI;EACb;EACA,IACEM,MAAM,CAACE,IAAI,KAAK,kBAAkB,KACjCF,MAAM,CAACH,QAAQ,KAAK,GAAG,IAAIG,MAAM,CAACH,QAAQ,KAAK,GAAG,CAAC,IACpDH,IAAI,KAAKM,MAAM,CAACsB,IAAI,EACpB;IACA,OAAO,IAAI;EACb;EACA,OAAOD,MAAM,CAAC3B,IAAI,EAAEM,MAAM,CAAC;AAC7B;AAMO,SAAS+B,iBAAiBA,CAC/BrC,IAAyB,EACzBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACED,UAAU,KAAK,aAAa,IAC3BA,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAAC6B,UAAU,KAAKnC,IAAK,IACpEO,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,gBAAgB,IAE/BA,UAAU,KAAK,iBAAiB,EAChC;IACA,OAAO,IAAI;EACb;EACA,IACE,CAACA,UAAU,KAAK,oBAAoB,IAAIA,UAAU,KAAK,aAAa,KACpED,MAAM,CAACgC,KAAK,CAAC,CAAC,CAAC,KAAKtC,IAAI,EACxB;IACA,OAAO,IAAI;EACb;EACA,IACEO,UAAU,KAAK,mBAAmB,KACjCD,MAAM,CAACiC,SAAS,KAAKvC,IAAI,IAAIM,MAAM,CAACkC,WAAW,KAAKxC,IAAI,CAAC,EAC1D;IACA,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEO,SAASyC,WAAWA,CAACzC,IAAmB,EAAEM,MAAc,EAAW;EACxE,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,oBAAoB,IACnCA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,aAAa,IAC3BA,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAAC6B,UAAU,KAAKnC,IAAK,IACpEO,UAAU,KAAK,gBAAgB;AAEnC;AAEO,SAASmC,kBAAkBA,CAChC1C,IAAmB,EACnBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,aAAa,IAC3BA,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAAC6B,UAAU,KAAKnC,IAAK,IACpEO,UAAU,KAAK,gBAAgB;AAEnC;AAEO,SAASoC,WAAWA,CAAC3C,IAAmB,EAAEM,MAAc,EAAW;EACxE,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACED,UAAU,KAAK,aAAa,IAC3BA,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAAC6B,UAAU,KAAKnC,IAAK,IACpEO,UAAU,KAAK,gBAAgB,EAC/B;IACA,OAAO,IAAI;EACb;EACA,IAAIP,IAAI,CAAC4C,aAAa,CAACC,UAAU,EAAE;IACjC,IACE,CAACtC,UAAU,KAAK,oBAAoB,IAAIA,UAAU,KAAK,aAAa,KACpED,MAAM,CAACgC,KAAK,CAAC,CAAC,CAAC,KAAKtC,IAAI,EACxB;MACA,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAEO,SAAS8C,cAAcA,CAC5B9C,IAAsB,EACtBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,aAAa,IAC3BA,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAAC6B,UAAU,KAAKnC,IAAK,IACpEO,UAAU,KAAK,gBAAgB;AAEnC;AAEO,SAASwC,yBAAyBA,CACvC/C,IAAiC,EACjCM,MAAc,EACd;EACA,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACE,CAACD,UAAU,KAAK,gBAAgB,IAC9BA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,2BAA2B,KAC5C,CAAC,CAIGD,MAAM,CAAC0C,cAAe;AAE9B;AAEO,SAASC,cAAcA,CAC5BjD,IAAsB,EACtBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,oBAAoB,IACnCA,UAAU,KAAK,aAAa,IAC5BA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,aAAa,IAC3BA,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAAC6B,UAAU,KAAKnC,IAAK,IACnEO,UAAU,KAAK,mBAAmB,KAChCD,MAAM,CAACiC,SAAS,KAAKvC,IAAI,IAAIM,MAAM,CAACkC,WAAW,KAAKxC,IAAI,CAAE;AAEjE;AAIO,SAASkD,gBAAgBA,CAC9BlD,IAAwB,EACxBM,MAAc,EACdU,YAAqB,EACrBmC,kBAA2B,EAClB;EAGT,OAAOnD,IAAI,CAACG,QAAQ,KAAK,IAAI,IAAIgD,kBAAkB;AACrD;AAEO,SAASC,kBAAkBA,CAChCpD,IAA0B,EAC1BM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACED,UAAU,KAAK,oBAAoB,IACnCA,UAAU,KAAK,yBAAyB,IACvCA,UAAU,KAAK,kBAAkB,IAAID,MAAM,CAAC+C,QAAQ,KAAKrD,IAAK,IAC9DO,UAAU,KAAK,0BAA0B,IAAID,MAAM,CAAC+C,QAAQ,KAAKrD,IAAK,IACvEO,UAAU,KAAK,iBAAiB,EAChC;IACA,OAAO,KAAK;EACd;EACA,IAAIA,UAAU,KAAK,kBAAkB,EAAE;IACrC,OAAO,IAAI;EACb;EACA,IAAIA,UAAU,KAAK,gBAAgB,EAAE;IACnC,OAAOD,MAAM,CAACyB,KAAK,KAAK/B,IAAI;EAC9B;EACA,IAAIO,UAAU,KAAK,0BAA0B,EAAE;IAC7C,OAAO,IAAI;EACb;EAEA,OAAO,CAACX,WAAW,CAACU,MAAM,CAAC;AAC7B;AAEO,SAASgD,eAAeA,CAC7BtD,IAAuB,EACvBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,IAClCA,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BG,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAC3BC,UAAU,KAAK,iBAAiB,IAAIZ,iBAAiB,CAACK,IAAI,CAAE,IAC5DO,UAAU,KAAK,uBAAuB,IAAIP,IAAI,KAAKM,MAAM,CAACiD,IAAK,IAChElD,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC,IAClCF,kBAAkB,CAACG,UAAU,CAAC;AAElC;AAIO,SAASiD,eAAeA,CAC7BxD,IAAuB,EACvBM,MAAc,EACdU,YAAoB,EACX;EACT,OAAOC,OAAO,CACZD,YAAY,IACTE,mBAAY,CAACI,mBAAmB,GAAGJ,mBAAY,CAACuC,aAAa,CAClE,CAAC;AACH;AAEO,SAASC,SAASA,CACvB1D,IAK0B,EAC1BM,MAAc,EACL;EACT,OACEI,cAAc,CAACV,IAAI,EAAEM,MAAM,CAAC,IAC3BlB,kBAAkB,CAACkB,MAAM,CAAC,IACzBA,MAAM,CAACH,QAAQ,KAAK,IAAI,IACxBG,MAAM,CAACsB,IAAI,KAAK5B,IAAK,IACvBK,oBAAoB,CAACL,IAAI,EAAEM,MAAM,CAAC;AAEtC;AAEO,SAASqD,kBAAkBA,CAChC3D,IAA0B,EAC1BM,MAAc,EACdU,YAAoB,EACX;EACT,OAAOC,OAAO,CACZD,YAAY,IACTE,mBAAY,CAACI,mBAAmB,GAAGJ,mBAAY,CAACuC,aAAa,CAClE,CAAC;AACH;AAEO,SAASG,qBAAqBA,CACnC5D,IAG0B,EAC1BM,MAAe,EACN;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACED,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,IACjCA,UAAU,KAAK,uBAAuB,IAAID,MAAM,CAACiD,IAAI,KAAKvD,IAAK,IAChEO,UAAU,KAAK,iBAAiB,IAChCH,kBAAkB,CAACG,UAAU,CAAC,EAC9B;IACA,OAAO,IAAI;EACb;EAEA,OAAOmD,SAAS,CAAC1D,IAAI,EAAEM,MAAM,CAAC;AAChC;AAIO,SAASuD,wBAAwBA,CACtC7D,IAAgC,EAChCM,MAAc,EACL;EACT,OACGjB,gBAAgB,CAACiB,MAAM,CAAC,IAAIA,MAAM,CAACM,MAAM,KAAKZ,IAAI,IAClDR,kBAAkB,CAACc,MAAM,CAAC,IAAIA,MAAM,CAACK,MAAM,KAAKX,IAAK;AAE1D;AAIO,SAAS8D,oBAAoBA,CAClC9D,IAA4B,EAC5BM,MAAc,EACdU,YAAoB,EACX;EACT,IACEK,+BAA+B,CAACL,YAAY,CAAC,IAC7CvB,eAAe,CAACO,IAAI,CAAC4B,IAAI,CAAC,EAC1B;IACA,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOgC,qBAAqB,CAAC5D,IAAI,EAAEM,MAAM,CAAC;EAC5C;AACF;AAEO,SAASyD,iBAAiBA,CAC/B/D,IAAyB,EACzBM,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IAAIJ,kBAAkB,CAACG,UAAU,CAAC,EAAE,OAAO,IAAI;EAC/C,IAAIA,UAAU,KAAK,mBAAmB,EAAE,OAAO,KAAK;EACpD,QAAQP,IAAI,CAACG,QAAQ;IACnB,KAAK,IAAI;MACP,OAAOG,MAAM,CAACH,QAAQ,KAAK,IAAI,IAAIG,MAAM,CAACH,QAAQ,KAAK,IAAI;IAC7D,KAAK,IAAI;MACP,OAAOG,MAAM,CAACH,QAAQ,KAAK,IAAI;IACjC,KAAK,IAAI;MACP,OAAOG,MAAM,CAACH,QAAQ,KAAK,IAAI;EACnC;AACF;AAEO,SAAS6D,UAAUA,CACxBhE,IAAkB,EAClBM,MAAc,EACdU,YAAoB,EACpBiD,UAAmB,EACnBC,gBAAgD,EACvC;EAAA,IAAAC,WAAA;EACT,MAAM5D,UAAU,GAAGD,MAAM,CAACE,IAAI;EAG9B,IACE,CAAA2D,WAAA,GAAAnE,IAAI,CAACoE,KAAK,aAAVD,WAAA,CAAYE,aAAa,IACzB9D,UAAU,KAAK,sBAAsB,IACrCD,MAAM,CAACsB,IAAI,KAAK5B,IAAI,EACpB;IACA,MAAMsE,SAAS,GAAGhE,MAAM,CAACyB,KAAK,CAACvB,IAAI;IACnC,IACE,CAAC8D,SAAS,KAAK,oBAAoB,IAAIA,SAAS,KAAK,iBAAiB,KACtEhE,MAAM,CAACyB,KAAK,CAACwC,EAAE,IAAI,IAAI,EACvB;MACA,OAAO,IAAI;IACb;EACF;EAEA,IAAIL,gBAAgB,IAAIA,gBAAgB,CAAClE,IAAI,CAAC,KAAKA,IAAI,CAACwE,IAAI,EAAE;IAC5D,OAAO,KAAK;EACd;EAIA,IAAIxE,IAAI,CAACwE,IAAI,KAAK,KAAK,EAAE;IAGvB,MAAMC,mBAAmB,GACvBjF,kBAAkB,CAACc,MAAM,EAAE;MACzBK,MAAM,EAAEX,IAAI;MACZ0E,QAAQ,EAAE;IACZ,CAAC,CAAC,IACFhF,0BAA0B,CAACY,MAAM,EAAE;MACjCK,MAAM,EAAEX,IAAI;MACZ0E,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACJ,IACEF,mBAAmB,IACnBzD,YAAY,IACTE,mBAAY,CAACI,mBAAmB,GAC/BJ,mBAAY,CAAC0D,OAAO,GACpB1D,mBAAY,CAAC2D,SAAS,CAAC,EAC3B;MACA,OAAO,IAAI;IACb;IACA,OAAO5D,OAAO,CAACD,YAAY,GAAGE,mBAAY,CAAC4D,SAAS,CAAC;EACvD;EAKA,OACE9E,IAAI,CAACwE,IAAI,KAAK,OAAO,IACrBlF,gBAAgB,CAACgB,MAAM,EAAE;IAAEsB,IAAI,EAAE5B,IAAI;IAAE+E,KAAK,EAAE;EAAM,CAAC,CAAC;AAE1D", "ignoreList": []}