import{r as e,j as t,b as n,R as a}from"./react-vendor-03c8a839.js";import{u as s,w as r,r as i}from"./xlsx-vendor-3e8bf635.js";import{a as o,d as l}from"./data-9da80832.js";import{A as c,c as d,g as m,a as h,b as u}from"./reports-cbeae2fd.js";import{u as p,L as x,b as g,a as v}from"./i18n-6ade161d.js";import{S as b}from"./media-82034546.js";import{t as f}from"./thermal-1b6f1885.js";import"./vendor-38106ca9.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const j=({onActivationSuccess:n})=>{const{t:a,currentLanguage:s,getCurrentLanguageConfig:r}=p(),[i,l]=e.useState(""),[c,d]=e.useState(!1),[m,h]=e.useState(""),[u,x]=e.useState(""),[g,v]=e.useState(!1),[b,f]=e.useState(""),j=r();e.useEffect((()=>{const e=o.generateMachineFingerprint();x(e)}),[]),e.useEffect((()=>{const e=e=>{const t=b+e.key.toLowerCase();f(t),(t.includes("reset")||t.includes("test"))&&(v(!0),f("")),setTimeout((()=>{f("")}),3e3)};return document.addEventListener("keypress",e),()=>document.removeEventListener("keypress",e)}),[b]);const y=async()=>{if(i.trim()){d(!0),h("");try{const e=o.activateProgram(i.trim());if(e.success){if("TRIAL"===e.data.type){e.data.trialDays,new Date(e.data.expiryDate).toLocaleDateString("ar-DZ");h("")}n(e.data)}else{let t=e.error;t.includes("البرنامج مفعل بالفعل على هذا الجهاز")?t=a("programAlreadyActivated"):t.includes("تنسيق كود التفعيل غير صحيح")&&(t=a("invalidActivationCodeFormat")),h(t)}}catch(e){h(a("unexpectedActivationError"))}finally{d(!1)}}else h(a("pleaseEnterActivationCode"))};return t.jsxs("div",{className:"activation-overlay",dir:j.direction,children:[t.jsxs("div",{className:"activation-dialog",children:[t.jsx("div",{className:"activation-header",children:t.jsxs("div",{className:"activation-logo",children:[t.jsx("h1",{children:"🏪 iCalDZ"}),t.jsx("p",{children:a("systemDescription","نظام المحاسبة المتكامل")})]})}),t.jsxs("div",{className:"activation-content",children:[t.jsx("h2",{children:a("activationTitle")}),t.jsx("p",{className:"activation-description",children:a("activationDescription")}),t.jsxs("div",{className:"activation-form",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{htmlFor:"activationCode",children:a("activationCodeLabel")}),t.jsx("input",{type:"text",id:"activationCode",value:i,onChange:e=>{let t=e.target.value.toUpperCase();t=t.replace(/[^A-Z0-9-]/g,""),l(t),h("")},onKeyPress:e=>{"Enter"===e.key&&y()},placeholder:a("activationCodePlaceholder"),className:"activation-input "+(m?"error":""),disabled:c,maxLength:50})]}),m&&t.jsx("div",{className:"activation-error",children:t.jsxs("span",{children:["⚠️ ",m]})}),t.jsx("button",{onClick:y,disabled:c||!i.trim(),className:"activation-button",children:c?t.jsxs(t.Fragment,{children:[t.jsx("span",{className:"loading-spinner",children:"⏳"}),a("activating")]}):t.jsxs(t.Fragment,{children:["🔑 ",a("activateProgram")]})}),g&&t.jsxs("button",{onClick:()=>{window.confirm(a("confirmResetActivation"))&&(o.resetActivation(),h(""),l(""),alert(a("resetActivationSuccess")),window.location.reload())},className:"reset-button",title:a("resetActivationTooltip"),children:["🔄 ",a("resetActivation")]})]}),t.jsxs("div",{className:"machine-info",children:[t.jsx("h3",{children:a("deviceInfo")}),t.jsxs("div",{className:"machine-id",children:[t.jsxs("span",{children:[a("deviceId")," "]}),t.jsx("code",{children:u})]}),t.jsxs("p",{className:"machine-note",children:["💡 ",a("deviceNote")]})]}),t.jsx("div",{className:"activation-footer",children:t.jsxs("div",{className:"contact-info",children:[t.jsx("h4",{children:a("getActivationCode")}),t.jsxs("p",{children:["📞 ",a("phone")," +213 551 93 05 89"]}),t.jsxs("p",{children:["📧 ",a("email")," <EMAIL>"]}),t.jsxs("p",{children:["🌐 ",a("website")," www.icodedz.com"]})]})})]})]}),t.jsx("style",{jsx:!0,children:'\n        .activation-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 10000;\n          font-family: \'Cairo\', sans-serif;\n        }\n\n        .activation-dialog {\n          background: white;\n          border-radius: 20px;\n          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n          max-width: 500px;\n          width: 90%;\n          max-height: 90vh;\n          overflow-y: auto;\n          animation: slideIn 0.5s ease-out;\n        }\n\n        @keyframes slideIn {\n          from {\n            opacity: 0;\n            transform: translateY(-50px) scale(0.9);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        .activation-header {\n          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);\n          color: white;\n          padding: 30px;\n          text-align: center;\n          border-radius: 20px 20px 0 0;\n        }\n\n        .activation-logo h1 {\n          margin: 0;\n          font-size: 2.5rem;\n          font-weight: bold;\n        }\n\n        .activation-logo p {\n          margin: 5px 0 0 0;\n          opacity: 0.9;\n          font-size: 1.1rem;\n        }\n\n        .activation-content {\n          padding: 30px;\n        }\n\n        .activation-content h2 {\n          text-align: center;\n          color: #2c3e50;\n          margin: 0 0 15px 0;\n          font-size: 1.8rem;\n        }\n\n        .activation-description {\n          text-align: center;\n          color: #666;\n          margin-bottom: 30px;\n          line-height: 1.6;\n        }\n\n        /* Language-specific text alignment */\n        [dir="rtl"] .activation-content h2,\n        [dir="rtl"] .activation-description,\n        [dir="rtl"] .contact-info h4,\n        [dir="rtl"] .contact-info p {\n          text-align: right;\n        }\n\n        [dir="ltr"] .activation-content h2,\n        [dir="ltr"] .activation-description,\n        [dir="ltr"] .contact-info h4,\n        [dir="ltr"] .contact-info p {\n          text-align: left;\n        }\n\n        /* Center alignment for titles in all languages */\n        .activation-content h2,\n        .contact-info h4 {\n          text-align: center !important;\n        }\n\n        .form-group {\n          margin-bottom: 20px;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 8px;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        /* Language-specific label alignment */\n        [dir="rtl"] .form-group label {\n          text-align: right;\n        }\n\n        [dir="ltr"] .form-group label {\n          text-align: left;\n        }\n\n        .activation-input {\n          width: 100%;\n          padding: 15px;\n          border: 2px solid #e0e0e0;\n          border-radius: 10px;\n          font-size: 14px;\n          font-family: \'Courier New\', monospace;\n          text-align: center;\n          transition: all 0.3s ease;\n          box-sizing: border-box;\n        }\n\n        .activation-input:focus {\n          outline: none;\n          border-color: #16a085;\n          box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);\n        }\n\n        .activation-input.error {\n          border-color: #e74c3c;\n          box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);\n        }\n\n        .activation-error {\n          background: #ffe6e6;\n          color: #c0392b;\n          padding: 12px;\n          border-radius: 8px;\n          margin: 15px 0;\n          text-align: center;\n          border: 1px solid #f5b7b1;\n        }\n\n        .activation-button {\n          width: 100%;\n          padding: 15px;\n          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);\n          color: white;\n          border: none;\n          border-radius: 10px;\n          font-size: 16px;\n          font-weight: 600;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 10px;\n        }\n\n        .activation-button:hover:not(:disabled) {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(22, 160, 133, 0.3);\n        }\n\n        .activation-button:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n          transform: none;\n        }\n\n        .reset-button {\n          width: 100%;\n          padding: 12px;\n          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n          color: white;\n          border: none;\n          border-radius: 8px;\n          font-size: 14px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          margin-top: 10px;\n          opacity: 0.8;\n        }\n\n        .reset-button:hover {\n          opacity: 1;\n          transform: translateY(-1px);\n          box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);\n        }\n\n        .loading-spinner {\n          animation: spin 1s linear infinite;\n        }\n\n        @keyframes spin {\n          from { transform: rotate(0deg); }\n          to { transform: rotate(360deg); }\n        }\n\n        .machine-info {\n          background: #f8f9fa;\n          padding: 20px;\n          border-radius: 10px;\n          margin: 25px 0;\n          border: 1px solid #e9ecef;\n        }\n\n        .machine-info h3 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        /* Language-specific machine info alignment */\n        [dir="rtl"] .machine-info h3,\n        [dir="rtl"] .machine-id,\n        [dir="rtl"] .machine-note {\n          text-align: right;\n        }\n\n        [dir="ltr"] .machine-info h3,\n        [dir="ltr"] .machine-id,\n        [dir="ltr"] .machine-note {\n          text-align: left;\n        }\n\n        .machine-id {\n          background: white;\n          padding: 10px;\n          border-radius: 5px;\n          border: 1px solid #ddd;\n          margin-bottom: 10px;\n        }\n\n        .machine-id code {\n          font-family: \'Courier New\', monospace;\n          color: #16a085;\n          font-weight: bold;\n        }\n\n        .machine-note {\n          font-size: 0.9rem;\n          color: #666;\n          margin: 0;\n          font-style: italic;\n        }\n\n        .activation-footer {\n          border-top: 1px solid #e9ecef;\n          padding-top: 20px;\n          margin-top: 20px;\n        }\n\n        .contact-info h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          text-align: center;\n        }\n\n        .contact-info p {\n          margin: 8px 0;\n          color: #666;\n          text-align: center;\n          font-size: 0.9rem;\n        }\n      '})]})};const y=new class{constructor(){this.isInitialized=!1,this.startTime=Date.now(),this.cleanupIntervals=new Set,this.activeTimeouts=new Set,this.activeIntervals=new Set,this.eventListeners=new Map,this.domObservers=new Set,this.memoryStats={totalCleanups:0,timeoutsCleared:0,intervalsCleared:0,listenersRemoved:0,lastCleanup:Date.now(),memoryUsage:0},this.systemHealth={isHealthy:!0,lastActivity:Date.now(),errorCount:0,warningCount:0,criticalErrors:[]},this.init()}init(){try{this.setupPeriodicCleanup(),this.setupMemoryMonitoring(),this.setupEmergencyRecovery(),this.setupHealthMonitoring(),this.isInitialized=!0,console.log("🧠 Memory Manager: System initialized successfully")}catch(e){console.error("🧠 Memory Manager: Initialization failed",e),this.systemHealth.criticalErrors.push({error:e.message,timestamp:Date.now(),context:"initialization"})}}setupPeriodicCleanup(){const e=setInterval((()=>{this.performMainCleanup()}),3e5),t=setInterval((()=>{this.performDeepCleanup()}),18e5),n=setInterval((()=>{this.performEmergencyCleanup()}),36e5);this.cleanupIntervals.add(e),this.cleanupIntervals.add(t),this.cleanupIntervals.add(n)}setupMemoryMonitoring(){if(performance&&performance.memory){const e=setInterval((()=>{this.checkMemoryUsage()}),12e4);this.cleanupIntervals.add(e)}}setupEmergencyRecovery(){const e=setInterval((()=>{this.checkForSystemBlocking()}),6e5);this.cleanupIntervals.add(e)}setupHealthMonitoring(){const e=setInterval((()=>{this.updateSystemHealth()}),3e5);this.cleanupIntervals.add(e)}registerTimeout(e,t="unknown"){return this.activeTimeouts.add({id:e,context:t,created:Date.now()}),e}registerInterval(e,t="unknown"){return this.activeIntervals.add({id:e,context:t,created:Date.now()}),e}registerEventListener(e,t,n,a="unknown"){const s=`${e.constructor.name}-${t}-${a}`;this.eventListeners.set(s,{element:e,event:t,handler:n,context:a,created:Date.now()})}clearTimeout(e){clearTimeout(e),this.activeTimeouts.forEach((t=>{t.id===e&&(this.activeTimeouts.delete(t),this.memoryStats.timeoutsCleared++)}))}clearInterval(e){clearInterval(e),this.activeIntervals.forEach((t=>{t.id===e&&(this.activeIntervals.delete(t),this.memoryStats.intervalsCleared++)}))}performMainCleanup(){console.log("🧠 Memory Manager: Performing main cleanup...");try{this.clearOldTimeouts(36e5),this.clearOldIntervals(72e5),this.cleanupDOMElements(),this.clearScannerTimeouts(),this.memoryStats.totalCleanups++,this.memoryStats.lastCleanup=Date.now(),console.log("🧠 Memory Manager: Main cleanup completed")}catch(e){console.error("🧠 Memory Manager: Main cleanup failed",e),this.systemHealth.errorCount++}}performDeepCleanup(){console.log("🧠 Memory Manager: Performing deep cleanup...");try{this.clearAllTimeouts(),this.removeOldEventListeners(),this.forceGarbageCollection(),this.resetScannerSystem(),console.log("🧠 Memory Manager: Deep cleanup completed")}catch(e){console.error("🧠 Memory Manager: Deep cleanup failed",e),this.systemHealth.errorCount++}}performEmergencyCleanup(){console.log("🧠 Memory Manager: Performing emergency cleanup...");try{this.clearAllTimeouts(),this.clearAllIntervals(),this.removeAllEventListeners(),this.resetAllSystems();for(let e=0;e<3;e++)setTimeout((()=>this.forceGarbageCollection()),1e3*e);console.log("🧠 Memory Manager: Emergency cleanup completed")}catch(e){console.error("🧠 Memory Manager: Emergency cleanup failed",e),this.systemHealth.criticalErrors.push({error:e.message,timestamp:Date.now(),context:"emergency_cleanup"})}}clearOldTimeouts(e){const t=Date.now();this.activeTimeouts.forEach((n=>{t-n.created>e&&(clearTimeout(n.id),this.activeTimeouts.delete(n),this.memoryStats.timeoutsCleared++)}))}clearOldIntervals(e){const t=Date.now();this.activeIntervals.forEach((n=>{t-n.created>e&&(clearInterval(n.id),this.activeIntervals.delete(n),this.memoryStats.intervalsCleared++)}))}clearAllTimeouts(){this.activeTimeouts.forEach((e=>{clearTimeout(e.id),this.memoryStats.timeoutsCleared++})),this.activeTimeouts.clear()}clearAllIntervals(){this.activeIntervals.forEach((e=>{clearInterval(e.id),this.memoryStats.intervalsCleared++})),this.activeIntervals.clear()}removeOldEventListeners(){const e=Date.now();this.eventListeners.forEach(((t,n)=>{if(e-t.created>72e5)try{t.element.removeEventListener(t.event,t.handler),this.eventListeners.delete(n),this.memoryStats.listenersRemoved++}catch(a){console.warn("🧠 Memory Manager: Failed to remove event listener",a)}}))}removeAllEventListeners(){this.eventListeners.forEach(((e,t)=>{try{e.element.removeEventListener(e.event,e.handler),this.memoryStats.listenersRemoved++}catch(n){console.warn("🧠 Memory Manager: Failed to remove event listener",n)}})),this.eventListeners.clear()}cleanupDOMElements(){document.querySelectorAll('[data-cleanup="true"]').forEach((e=>{try{e.remove()}catch(t){console.warn("🧠 Memory Manager: Failed to remove DOM element",t)}}));document.querySelectorAll('iframe[style*="position: absolute"]').forEach((e=>{try{"-9999px"===e.style.left&&e.remove()}catch(t){console.warn("🧠 Memory Manager: Failed to remove print iframe",t)}}))}clearScannerTimeouts(){["dashboardScannerTimeout","salesScannerTimeout","salesScannerValidationTimeout","editScannerValidationTimeout","productScannerTimeout"].forEach((e=>{window[e]&&(clearTimeout(window[e]),window[e]=null)}))}resetScannerSystem(){window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0)),this.clearScannerTimeouts()}resetAllSystems(){if(this.resetScannerSystem(),window.KeyboardShortcuts&&window.KeyboardShortcuts.setEnabled(!0),window.SoundManager)try{window.SoundManager.cleanup()}catch(e){console.warn("🧠 Memory Manager: Failed to cleanup sound manager",e)}}forceGarbageCollection(){if(window.gc&&"function"==typeof window.gc)try{window.gc(),console.log("🧠 Memory Manager: Forced garbage collection")}catch(e){console.warn("🧠 Memory Manager: Garbage collection not available",e)}}checkMemoryUsage(){if(performance&&performance.memory){const e=performance.memory;this.memoryStats.memoryUsage=e.usedJSHeapSize;const t=104857600;e.usedJSHeapSize>t&&(console.warn("🧠 Memory Manager: High memory usage detected",{used:Math.round(e.usedJSHeapSize/1024/1024)+"MB",total:Math.round(e.totalJSHeapSize/1024/1024)+"MB"}),this.performEmergencyCleanup())}}checkForSystemBlocking(){Date.now()-this.systemHealth.lastActivity>18e5&&(console.warn("🧠 Memory Manager: Potential system blocking detected"),this.performEmergencyCleanup(),this.systemHealth.warningCount++)}updateSystemHealth(){const e=Date.now(),t=e-this.startTime;this.systemHealth.lastActivity=e,this.systemHealth.isHealthy=this.systemHealth.errorCount<10,t%36e5<3e5&&console.log("🧠 Memory Manager: System health status",{uptime:Math.round(t/1e3/60)+" minutes",isHealthy:this.systemHealth.isHealthy,errorCount:this.systemHealth.errorCount,memoryUsage:Math.round(this.memoryStats.memoryUsage/1024/1024)+"MB",totalCleanups:this.memoryStats.totalCleanups})}getStatus(){return{initialized:this.isInitialized,uptime:Date.now()-this.startTime,memoryStats:this.memoryStats,systemHealth:this.systemHealth,activeTimeouts:this.activeTimeouts.size,activeIntervals:this.activeIntervals.size,eventListeners:this.eventListeners.size}}destroy(){console.log("🧠 Memory Manager: Shutting down..."),this.cleanupIntervals.forEach((e=>clearInterval(e))),this.cleanupIntervals.clear(),this.performEmergencyCleanup(),this.isInitialized=!1,console.log("🧠 Memory Manager: Shutdown completed")}};function N(){const{t:n,isLanguageSelected:a,setIsLanguageSelected:x,currentLanguage:N,getCurrentLanguageConfig:w}=p();e.useEffect((()=>{window.barcodeShortcutManager||(window.barcodeShortcutManager={isEnabled:!0,isBarcodeActive:!1,checkBarcodeInput:e=>{if(!e||"INPUT"!==e.tagName)return!1;const t=["barcode-input","scanner-input","dashboard-scanner","sales-scanner","edit-scanner","product-barcode"];if(e.className){const n=e.className.toLowerCase();if(t.some((e=>n.includes(e))))return!0}if(e.id){const n=e.id.toLowerCase();if(t.some((e=>n.includes(e))))return!0}if(e.placeholder){const t=e.placeholder.toLowerCase();if(t.includes("barcode")||t.includes("باركود")||t.includes("scanner")||t.includes("مسح"))return!0}return!1},setShortcutsEnabled:e=>{window.KeyboardShortcuts&&(window.KeyboardShortcuts.setEnabled(e),console.log("🔧 BARCODE FIX: Shortcuts "+(e?"enabled":"disabled")))}},console.log("🔧 BARCODE FIX: Shortcut manager initialized"));const e=e=>{window.barcodeShortcutManager&&window.barcodeShortcutManager.checkBarcodeInput(e.target)&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1),console.log("🔧 BARCODE FIX: Shortcuts disabled - barcode input focused"))},t=e=>{window.barcodeShortcutManager&&setTimeout((()=>{const e=document.activeElement;window.barcodeShortcutManager.checkBarcodeInput(e)||(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Shortcuts re-enabled - barcode input lost focus"))}),100)};return document.addEventListener("focusin",e,!0),document.addEventListener("focusout",t,!0),()=>{document.removeEventListener("focusin",e,!0),document.removeEventListener("focusout",t,!0)}}),[]);const $=e=>{if(!e)return"";let t=String(e).replace(/[\r\n\t\f\v]/g,"").replace(/[^\w\d\-_.]/g,"").replace(/\s+/g,"").trim();return t.length>50&&(t=t.substring(0,50)),t},S=e=>{if(!e||"string"!=typeof e)return!1;const t=e.trim();if(t.length<3||t.length>50)return!1;return/^[a-zA-Z0-9\-_\.]{3,50}$/.test(t)},[k,C]=e.useState({lastActivity:Date.now(),totalScans:0,errors:0,isHealthy:!0});e.useEffect((()=>{const e=setInterval((()=>{const e=Date.now();if(e-k.lastActivity>36e5&&(console.log("🔧 BARCODE FIX: Resetting scanner health after inactivity"),C({lastActivity:e,totalScans:0,errors:0,isHealthy:!0}),D()),e%6e5<3e5&&(D(),console.log("🔧 BARCODE FIX: Periodic cleanup performed"),y&&y.performMainCleanup()),k.errors>10&&(console.log("🔧 BARCODE FIX: Too many scanner errors - performing auto-recovery"),C({lastActivity:e,totalScans:0,errors:0,isHealthy:!0}),D(),window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0)),y&&y.performEmergencyCleanup()),y){y.getStatus().systemHealth.isHealthy||(console.warn("🧠 SYSTEM HEALTH: Memory manager reports unhealthy status - triggering recovery"),y.performDeepCleanup(),I())}}),3e5);return y&&y.registerInterval(e,"health_monitoring"),()=>{y?y.clearInterval(e):clearInterval(e)}}),[k.lastActivity,k.errors]);const D=()=>{let e=0;if(window.dashboardScannerTimeout&&(y?y.clearTimeout(window.dashboardScannerTimeout):clearTimeout(window.dashboardScannerTimeout),window.dashboardScannerTimeout=null,e++),window.salesScannerTimeout&&(y?y.clearTimeout(window.salesScannerTimeout):clearTimeout(window.salesScannerTimeout),window.salesScannerTimeout=null,e++),window.salesScannerValidationTimeout&&(y?y.clearTimeout(window.salesScannerValidationTimeout):clearTimeout(window.salesScannerValidationTimeout),window.salesScannerValidationTimeout=null,e++),window.editScannerValidationTimeout&&(y?y.clearTimeout(window.editScannerValidationTimeout):clearTimeout(window.editScannerValidationTimeout),window.editScannerValidationTimeout=null,e++),window.productScannerTimeout&&(y?y.clearTimeout(window.productScannerTimeout):clearTimeout(window.productScannerTimeout),window.productScannerTimeout=null,e++),y)y.forceGarbageCollection();else if(window.gc&&"function"==typeof window.gc)try{window.gc()}catch(t){}console.log(`🔧 BARCODE FIX: Scanner timeouts cleared (${e} timeouts)`),((e=!0,t="scan")=>{C((n=>{const a=e?n.errors:n.errors+1,s=a<10;return!s&&n.isHealthy?console.warn("🔧 BARCODE FIX: Scanner health degraded - too many errors"):s&&!n.isHealthy&&console.log("🔧 BARCODE FIX: Scanner health recovered"),{lastActivity:Date.now(),totalScans:"cleanup"===t?n.totalScans:n.totalScans+1,errors:a,isHealthy:s,systemStatus:s?"active":"degraded",lastContext:t}}))})(!0,"cleanup")},I=()=>{console.log("🔧 BARCODE FIX: Emergency scanner system reset initiated"),D(),Pe(""),qa(""),_a(""),Re(null),Va(null),Wa(null),C({lastActivity:Date.now(),totalScans:0,errors:0,isHealthy:!0,systemStatus:"active",lastContext:"manual_reset"}),window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Shortcut manager reset")),setTimeout((()=>{"dashboard"===J&&Ee.current&&(Ee.current.focus(),console.log("🔧 BARCODE FIX: Dashboard scanner re-focused after reset"))}),500),Jt("🔧 نظام الباركود تم إعادة تعيينه بنجاح","success",3e3),console.log("🔧 BARCODE FIX: Emergency scanner system reset completed")},[M,T]=e.useState(!1),[P,E]=e.useState(!1),[A,R]=e.useState(!0),[O,F]=e.useState(!0),[L,z]=e.useState(!0),[B,U]=e.useState(!0),[q,G]=e.useState(!1);e.useEffect((()=>{(()=>{const e=o.checkActivationStatus();T(e.activated),E(!0),e.activated?"TRIAL"===e.type&&(console.log(`Trial activation: ${e.daysLeft} days left`),Jt(`🕐 فترة تجربة: ${e.daysLeft} أيام متبقية`,"info",5e3)):console.log("Activation required:",e.reason)})()}),[]),e.useEffect((()=>{M&&(async()=>{try{await b.init();const e=b.getStatus();R(e.isEnabled),y&&!y.isInitialized&&(await y.init(),console.log("🧠 Memory Manager initialized for system stability"));const t=localStorage.getItem("printerEnabled");null!==t&&z(JSON.parse(t));const n=localStorage.getItem("shortcutsEnabled");null!==n&&F(JSON.parse(n));const a=localStorage.getItem("notificationsEnabled");null!==a&&U(JSON.parse(a)),console.log("🎵 Audio system initialized");const s=setTimeout((()=>{b.play("success",{showNotification:!1})}),1e3);y&&y.registerTimeout(s,"welcome_sound")}catch(e){console.error("🔇 Failed to initialize audio system:",e)}})()}),[M]),e.useEffect((()=>{const e=()=>{const e=window.pageYOffset||document.documentElement.scrollTop;G(e>300)};return window.addEventListener("scroll",e),e(),()=>window.removeEventListener("scroll",e)}),[]);const V=e=>{console.log("Language selected:",e),x(!0)},Z={isLoggedIn:"true"===localStorage.getItem("icaldz-login-status"),page:localStorage.getItem("icaldz-current-page")||"dashboard"},[J,_]=e.useState(Z.isLoggedIn?Z.page:"login"),[H,W]=e.useState(Z.isLoggedIn),[Y,K]=e.useState(null),[Q,X]=e.useState(!1),[ee,te]=e.useState(!1),[ne,ae]=e.useState(!1);e.useState(!1),e.useState(null),e.useState(!1),e.useState(null);const[se,re]=e.useState(!1),[ie,oe]=e.useState(null),[le,ce]=e.useState(!1),[de,me]=e.useState(null),[he,ue]=e.useState(!1),[pe,xe]=e.useState(null),[ge,ve]=e.useState(0),[be,fe]=e.useState("نقداً");e.useState("");const[je,ye]=e.useState(""),[Ne,we]=e.useState(""),[$e,Se]=e.useState(!1),[ke,Ce]=e.useState({username:"",password:""}),De=(e,t="dashboard")=>{localStorage.setItem("icaldz-login-status",e.toString()),localStorage.setItem("icaldz-current-page",t)},[Ie,Me]=e.useState((()=>{const e=localStorage.getItem("icaldz-invoices");return e?JSON.parse(e):[]})),[Te,Pe]=e.useState(""),Ee=e.useRef(null),[Ae,Re]=e.useState(null),[Oe,Fe]=e.useState(null),[Le,ze]=e.useState(null),[Be,Ue]=e.useState(null),[qe,Ge]=e.useState([]);e.useEffect((()=>{ee&&Za.current&&setTimeout((()=>{Za.current.focus()}),100)}),[ee]),e.useEffect((()=>{"dashboard"===J&&Ee.current&&setTimeout((()=>{Ee.current.focus(),console.log("🎯 Dashboard: Auto-focused barcode input")}),200)}),[J]);const[Ve,Ze]=e.useState(!1),[Je,_e]=e.useState(null),[He,We]=e.useState(null),[Ye,Ke]=e.useState({invoiceNumber:"",date:"",supplierId:"",supplierName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0}),[Qe,Xe]=e.useState([]),[et,tt]=e.useState([]),nt=e=>{localStorage.setItem("icaldz-expenses",JSON.stringify(e)),st(e)},[at,st]=e.useState((()=>{const e=localStorage.getItem("icaldz-expenses");return e?JSON.parse(e):[]})),[rt,it]=e.useState(!1),[ot,lt]=e.useState({id:"",date:(new Date).toISOString().split("T")[0],category:"رواتب",amount:0,description:"",paymentMethod:"نقداً"}),[ct,dt]=e.useState(!1),[mt,ht]=e.useState(!1),[ut,pt]=e.useState(!1),[xt,gt]=e.useState(null),[vt,bt]=e.useState({id:"",name:"",phone:"",address:"",email:""}),[ft,jt]=e.useState(!1),[yt,Nt]=e.useState([]),[wt,$t]=e.useState([]),[St,kt]=e.useState([]),[Ct,Dt]=e.useState([]),[It,Mt]=e.useState([]),[Tt,Pt]=e.useState([]);e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState("");const[Et,At]=e.useState({storeName:"iCalDZ Store",storeNumber:"ST001",storePhone:"+*********** 456",storeAddress:"الجزائر العاصمة، الجزائر",storeLogo:"",taxRate:19,currency:"DZD"}),[Rt,Ot]=e.useState([]),[Ft,Lt]=e.useState(!1),[zt,Bt]=e.useState({role:"مدير",name:"المدير"}),[Ut,qt]=e.useState({id:"",name:"",username:"",password:"",phone:"",email:"",role:"seller",isActive:!0}),Gt=e=>{const t=(e||0).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),n="ar"===N?"د.ج":"DZD";return"ar"===N?`${n} ${t}`:`${t} ${n}`},Vt=e=>e&&"GUEST"!==e&&"زبون عابر"!==e&&"Client de passage"!==e&&"Walk-in Customer"!==e?e:n("walkInCustomer","زبون عابر"),Zt=e=>{if(!e||"مورد عام"===e||"Fournisseur général"===e||"General Supplier"===e)return n("generalSupplier","مورد عام");const t={"شركة التوريدات العامة":{fr:"Société de Fournitures Générales",en:"General Supply Company",ar:"شركة التوريدات العامة"},"مؤسسة الإمداد":{fr:"Institution d'Approvisionnement",en:"Supply Institution",ar:"مؤسسة الإمداد"},"شركة السلع الأولية":{fr:"Société de Matières Premières",en:"Raw Materials Company",ar:"شركة السلع الأولية"},"مؤسسة التجهيزات":{fr:"Institution d'Équipements",en:"Equipment Institution",ar:"مؤسسة التجهيزات"},"شركة المعدات الحديثة":{fr:"Société d'Équipements Modernes",en:"Modern Equipment Company",ar:"شركة المعدات الحديثة"},"مورد رقم واحد":{fr:"Fournisseur Numéro Un",en:"Supplier Number One",ar:"مورد رقم واحد"},"مورد رقم اثنين":{fr:"Fournisseur Numéro Deux",en:"Supplier Number Two",ar:"مورد رقم اثنين"},"مورد رقم ثلاثة":{fr:"Fournisseur Numéro Trois",en:"Supplier Number Three",ar:"مورد رقم ثلاثة"},"مورد محلي":{fr:"Fournisseur Local",en:"Local Supplier",ar:"مورد محلي"},"مورد خارجي":{fr:"Fournisseur Externe",en:"External Supplier",ar:"مورد خارجي"}};return t[e]&&t[e][N]||e},Jt=(e,t="success",n=3e3)=>{if(!B)return void console.log("🔕 Notification blocked (disabled):",e);const a=Date.now()+Math.random(),s={id:a,message:e,type:t,duration:n};Ge((e=>[...e,s])),setTimeout((()=>{_t(a)}),n)},_t=e=>{Ge((t=>t.filter((t=>t.id!==e))))},Ht=(e,t)=>{console.log("💰 Dashboard LCD: Showing total final for 6 seconds:",Gt(e)),((e,t)=>{const n={finalTotal:e,invoiceNumber:t,timestamp:(new Date).toLocaleTimeString(),date:(new Date).toLocaleDateString(),savedAt:(new Date).toISOString()};try{localStorage.setItem("icaldz-last-invoice-lcd",JSON.stringify(n)),Ue(n),console.log("💰 Dashboard LCD: Last invoice saved to localStorage:",n)}catch(a){console.error("❌ Error saving last invoice to localStorage:",a)}})(e,t),Le&&clearTimeout(Le),Fe({finalTotal:e,invoiceNumber:t,timestamp:(new Date).toLocaleTimeString()});const n=setTimeout((()=>{console.log("💰 Dashboard LCD: Reverting to last saved invoice display after 6 seconds"),Fe(null),ze(null)}),6e3);ze(n)},Wt=(e,t)=>{switch(e){case"sales":yt.length===t.length?Nt([]):Nt(t.map((e=>e.id)));break;case"purchases":wt.length===t.length?$t([]):$t(t.map((e=>e.id)));break;case"customers":St.length===t.length?kt([]):kt(t.map((e=>e.id)));break;case"products":Ct.length===t.length?Dt([]):Dt(t.map((e=>e.id)));break;case"suppliers":It.length===t.length?Mt([]):Mt(t.map((e=>e.id)));break;case"sellers":Tt.length===t.length?Pt([]):Pt(t.map((e=>e.id)))}},Yt=(e,t)=>{switch(e){case"sales":Nt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"purchases":$t((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"customers":kt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"products":Dt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"suppliers":Mt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"sellers":Pt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]))}},Kt=e=>{if("مدير"===zt.role||"admin"===zt.role)switch(e){case"sales":if(0===yt.length)return void Jt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${yt.length})\n\n${n("allProductsWillBeRestored","سيتم إرجاع جميع المنتجات إلى المخزون.")}`)){Ie.filter((e=>yt.includes(e.id))).forEach((e=>{e.items.forEach((e=>{const t=Mn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Mn];n[t].stock+=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}}))}));(e=>{try{localStorage.setItem("icaldz-invoices",JSON.stringify(e)),Me(e)}catch(t){console.error("❌ Error in saveInvoices:",t),Jt("❌ خطأ في حفظ الفاتورة","error",3e3)}})(Ie.filter((e=>!yt.includes(e.id)))),Nt([]),Jt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${yt.length}) ${n("andStockRestored","وإرجاع المنتجات للمخزون")}`,"success",3e3)}break;case"purchases":if(0===wt.length)return void Jt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${wt.length})\n\n${n("stockWillBeAdjusted","سيتم تعديل المخزون تلقائياً.")}`)){et.filter((e=>wt.includes(e.id))).forEach((e=>{e.items.forEach((e=>{const t=Mn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Mn];n[t].stock-=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}}))}));const e=et.filter((e=>!wt.includes(e.id)));tt(e),localStorage.setItem("icaldz-purchases",JSON.stringify(e)),$t([]),Jt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${wt.length}) ${n("andStockAdjusted","وتعديل المخزون")}`,"success",3e3)}break;case"customers":if(0===St.length)return void Jt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${St.length})`)){const e=fa.filter((e=>!St.includes(e.id)));ja(e),localStorage.setItem("icaldz-customers",JSON.stringify(e)),kt([]),Jt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${St.length})`,"success",3e3)}break;case"products":if(0===Ct.length)return void Jt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${Ct.length})`)){const e=Mn.filter((e=>!Ct.includes(e.id)));Tn(e),localStorage.setItem("icaldz-products",JSON.stringify(e)),Dt([]),Jt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${Ct.length})`,"success",3e3)}break;case"suppliers":if(0===It.length)return void Jt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${It.length})`)){const e=Qe.filter((e=>!It.includes(e.id)));Xe(e),localStorage.setItem("icaldz-suppliers",JSON.stringify(e)),Mt([]),Jt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${It.length})`,"success",3e3)}break;case"sellers":if(0===Tt.length)return void Jt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${Tt.length})`)){const e=Rt.filter((e=>!Tt.includes(e.id)));Ot(e),localStorage.setItem("icaldz-sellers",JSON.stringify(e)),Pt([]),Jt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${Tt.length})`,"success",3e3)}}else Jt(`❌ ${n("notAllowedManagerOnlyBulkDelete","غير مسموح - المدير فقط يمكنه الحذف المتعدد")}`,"error")},Qt=(e,t="inventory_export")=>{try{const a=s.book_new(),i=[n("productCode","رمز المنتج"),n("productName","اسم المنتج"),n("barcode","الباركود"),n("category","الفئة"),`${n("buyPrice","سعر الشراء")} (${n("currency","دج")})`,`${n("sellPrice","سعر البيع")} (${n("currency","دج")})`,n("availableQuantity","الكمية المتوفرة"),n("minStock","الحد الأدنى"),`${n("totalValue","القيمة الإجمالية")} (${n("currency","دج")})`,n("status","الحالة")],o=e.map((e=>{const t=e.stock<=e.minStock?n("lowStock","منخفض"):0===e.stock?n("outOfStock","نفد"):e.stock>2*e.minStock?n("highStock","مرتفع"):n("normalStock","عادي");return[e.id,e.name,e.barcode||n("notSpecified","غير محدد"),e.category,e.buyPrice,e.sellPrice,e.stock,e.minStock,e.stock*e.buyPrice,t]})),l=e.length,c=e.reduce(((e,t)=>e+t.stock*t.buyPrice),0),d=e.filter((e=>e.stock<=e.minStock)).length,m=e.filter((e=>0===e.stock)).length;o.push([]),o.push(["","","","","","","","","",""]),o.push([n("summary","الملخص"),"","","","","","","","",""]),o.push([n("totalProducts","إجمالي المنتجات"),l,"","","","","","","",""]),o.push([n("totalValue","القيمة الإجمالية"),c,"","","","","","","",""]),o.push([n("lowStockProducts","منتجات منخفضة المخزون"),d,"","","","","","","",""]),o.push([n("outOfStockProducts","منتجات نفدت"),m,"","","","","","","",""]);const h=[i,...o],u=s.aoa_to_sheet(h),p=[{wch:12},{wch:25},{wch:15},{wch:15},{wch:15},{wch:15},{wch:12},{wch:12},{wch:18},{wch:12}];u["!cols"]=p;const x=s.decode_range(u["!ref"]);for(let e=x.s.c;e<=x.e.c;e++){const t=s.encode_cell({r:0,c:e});u[t]&&(u[t].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"4472C4"}},alignment:{horizontal:"center",vertical:"center",readingOrder:2},border:{top:{style:"thin",color:{rgb:"000000"}},bottom:{style:"thin",color:{rgb:"000000"}},left:{style:"thin",color:{rgb:"000000"}},right:{style:"thin",color:{rgb:"000000"}}}})}const g=e.length+3;for(let e=g;e<g+5;e++){const t=s.encode_cell({r:e,c:0});u[t]&&(u[t].s={font:{bold:!0},alignment:{horizontal:"right",readingOrder:2}})}u["!dir"]="ar"===N?"rtl":"ltr";const v=n("inventoryReport","تقرير المخزون");s.book_append_sheet(a,u,v);const b=(new Date).toISOString().split("T")[0];return r(a,`${t}_${b}.xlsx`),!0}catch(a){return console.error("Error exporting to Excel:",a),!1}};e.useEffect((()=>{Xt(),en(),nn(),sn();const e=(()=>{try{const e=localStorage.getItem("icaldz-last-invoice-lcd");return e?JSON.parse(e):null}catch(e){return console.error("❌ Error loading last saved invoice:",e),null}})();e&&(Ue(e),console.log("💰 Dashboard LCD: Loaded last saved invoice from localStorage:",e))}),[]);const Xt=()=>{const e=localStorage.getItem("icaldz-settings");e&&At(JSON.parse(e))},en=()=>{const e=localStorage.getItem("icaldz-sellers");if(e)Ot(JSON.parse(e));else{const e=[{id:"S001",name:"مدير النظام",username:"admin",password:"admin",phone:"+213 555 000 000",email:"<EMAIL>",role:"admin",isActive:!0,createdAt:(new Date).toISOString()}];Ot(e),localStorage.setItem("icaldz-sellers",JSON.stringify(e))}},tn=e=>{l.saveData("icaldz-sellers",e),Ot(e)},nn=()=>{const e=localStorage.getItem("icaldz-suppliers");if(e)Xe(JSON.parse(e));else{const e=[{id:"SUP001",name:"شركة التوريدات العامة",phone:"+213 555 111 111",email:"<EMAIL>",address:"الجزائر العاصمة"},{id:"SUP002",name:"مؤسسة الإمداد",phone:"+213 555 222 222",email:"<EMAIL>",address:"وهران"},{id:"SUP003",name:"شركة السلع الأولية",phone:"+213 555 333 333",email:"<EMAIL>",address:"قسنطينة"}];Xe(e),localStorage.setItem("icaldz-suppliers",JSON.stringify(e))}},an=e=>{l.saveData("icaldz-suppliers",e),Xe(e)},sn=()=>{const e=localStorage.getItem("icaldz-purchases");e&&tt(JSON.parse(e))},rn=e=>{l.saveData("icaldz-purchases",e),tt(e)},on=()=>{dt(!0),bt({id:`SUP${String(Qe.length+1).padStart(3,"0")}`,name:"",phone:"",address:"",email:""})},ln=()=>{dt(!1),bt({id:"",name:"",phone:"",address:"",email:""})},cn=e=>{if(window.confirm(n("confirmDeleteSupplier","هل أنت متأكد من حذف هذا المورد؟"))){const t=Qe.filter((t=>t.id!==e));an(t),Jt(n("supplierDeletedSuccessfully","تم حذف المورد بنجاح"),"success")}},dn=()=>{gt(null),pt(!1)},mn=e=>{_e(e),Ze(!0)},hn=e=>{try{const t=localStorage.getItem("icaldz-settings"),n=t?JSON.parse(t):Et,a={storeName:n.storeName||"iCalDZ Store",storeNumber:n.storeNumber||"ST001",storePhone:n.storePhone||"+*********** 456",storeAddress:n.storeAddress||"الجزائر العاصمة، الجزائر",storeLogo:n.storeLogo||"",taxRate:n.taxRate||19,currency:n.currency||"DZD"};return console.log("🏪 Store settings synchronized for thermal printing:",a),f.printInvoice(e,{language:N,showToast:Jt,formatPrice:Gt,directPrint:L,storeSettings:a}),A&&b.play("printInvoice",{showNotification:!1}),void console.log("🖨️ Thermal invoice sent to printer:",e.invoiceNumber)}catch(t){console.error("🖨️ Thermal printing failed, using fallback:",t)}un(e)},un=e=>{const t="ar"===N,a=`\n      <!DOCTYPE html>\n      <html dir="${t?"rtl":"ltr"}" lang="${N}">\n      <head>\n        <meta charset="UTF-8">\n        <style>\n          @page {\n            size: 80mm auto;\n            margin: 0;\n          }\n\n          body {\n            font-family: 'Courier New', monospace;\n            font-size: 14px;\n            font-weight: bold;\n            line-height: 1.4;\n            margin: 0;\n            padding: 3mm;\n            width: 74mm;\n            color: black;\n            background: white;\n            direction: ${t?"rtl":"ltr"};\n            text-align: center;\n          }\n\n          .header {\n            text-align: center;\n            margin-bottom: 4mm;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n            position: relative;\n          }\n\n          .logo {\n            margin: 0 auto 2mm auto;\n            width: 15mm;\n            height: 15mm;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            overflow: hidden;\n            background: ${Et.storeLogo?"white":"linear-gradient(135deg, #007bff, #0056b3)"};\n            border: 2px solid #333;\n          }\n\n          .logo img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n            border-radius: 50%;\n          }\n\n          .logo-fallback {\n            color: white;\n            font-size: 8px;\n            font-weight: bold;\n          }\n\n          .store-name {\n            font-size: 18px;\n            font-weight: bold;\n            margin-bottom: 2mm;\n            text-transform: uppercase;\n          }\n\n          .store-info {\n            font-size: 12px;\n            font-weight: bold;\n            margin-bottom: 1mm;\n          }\n\n          .invoice-info {\n            margin: 3mm 0;\n            font-size: 12px;\n            text-align: center;\n          }\n\n          .invoice-info div {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 2mm;\n            font-weight: bold;\n          }\n\n          .items-header {\n            border-bottom: 2px solid black;\n            padding-bottom: 2mm;\n            margin-bottom: 2mm;\n            font-size: 14px;\n            font-weight: bold;\n            text-align: center;\n            text-transform: uppercase;\n          }\n\n          .item-row {\n            font-size: 12px;\n            margin-bottom: 2mm;\n            padding: 2mm 0;\n            border-bottom: 1px dashed #666;\n          }\n\n          .item-name {\n            font-weight: bold;\n            margin-bottom: 1mm;\n            text-align: center;\n            font-size: 13px;\n          }\n\n          .item-details {\n            display: flex;\n            justify-content: space-between;\n            font-weight: bold;\n          }\n\n          .totals {\n            border-top: 2px solid black;\n            padding-top: 3mm;\n            margin-top: 3mm;\n            font-size: 12px;\n          }\n\n          .total-row {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 2mm;\n            font-weight: bold;\n          }\n\n          .final-total {\n            font-weight: bold;\n            font-size: 16px;\n            border: 3px double black;\n            padding: 3mm;\n            margin: 3mm 0;\n            text-align: center;\n            background: #f0f0f0;\n          }\n\n          .footer {\n            text-align: center;\n            margin-top: 4mm;\n            padding-top: 3mm;\n            border-top: 2px solid black;\n            font-size: 10px;\n          }\n\n          .thank-you {\n            font-weight: bold;\n            margin-bottom: 2mm;\n            font-size: 14px;\n          }\n\n          .date-time {\n            font-size: 10px;\n            font-weight: bold;\n            margin-bottom: 3mm;\n          }\n\n          .developer-footer {\n            margin-top: 4mm;\n            padding-top: 2mm;\n            border-top: 1px dashed black;\n            font-size: 10px;\n            font-weight: bold;\n          }\n\n          .developer-name {\n            margin-bottom: 1mm;\n          }\n\n          .developer-phone {\n            font-size: 12px;\n            font-weight: bold;\n          }\n\n          /* Print specific styles */\n          @media print {\n            body {\n              width: 80mm !important;\n              font-size: 14px !important;\n              font-weight: bold !important;\n            }\n\n            .no-print {\n              display: none !important;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <div class="logo">\n            ${Et.storeLogo?`<img src="${Et.storeLogo}" alt="${Et.storeName}" />`:'<div class="logo-fallback">iC</div>'}\n          </div>\n          <div class="store-name">${Et.storeName}</div>\n          <div class="store-info">${Et.storePhone}</div>\n          <div class="store-info">${Et.storeAddress}</div>\n        </div>\n\n        <div class="invoice-info">\n          <div>\n            <span>${n("invoiceNumberLabel","فاتورة رقم:")}</span>\n            <span>${e.invoiceNumber}</span>\n          </div>\n          <div>\n            <span>${n("dateLabel","التاريخ:")}</span>\n            <span>${e.date}</span>\n          </div>\n          <div>\n            <span>${n("customerLabel","الزبون:")}</span>\n            <span>${e.customerName||n("walkInCustomer","زبون عابر")}</span>\n          </div>\n          <div>\n            <span>${n("paymentMethodLabel","طريقة الدفع:")}</span>\n            <span>${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("cash","نقداً"):"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}</span>\n          </div>\n        </div>\n\n        <div class="items-header">\n          ${n("productsLabel","المنتجات")}\n        </div>\n\n        <div class="items-list">\n          ${e.items.map((e=>`\n            <div class="item-row">\n              <div class="item-name">${e.productName}</div>\n              <div class="item-details">\n                <span>${e.quantity} × ${Gt(e.price)}</span>\n                <span>${Gt(e.total)}</span>\n              </div>\n            </div>\n          `)).join("")}\n        </div>\n\n        <div class="totals">\n          <div class="total-row">\n            <span>${n("subtotalLabel","المجموع الفرعي:")}</span>\n            <span>${Gt(e.total)}</span>\n          </div>\n          ${e.discount>0?`\n            <div class="total-row">\n              <span>${n("discountLabel","الخصم:")}</span>\n              <span>-${Gt(e.discount)}</span>\n            </div>\n          `:""}\n          <div class="total-row">\n            <span>${n("taxLabel","الضريبة")} (${Et.taxRate}%):</span>\n            <span>${Gt(e.tax)}</span>\n          </div>\n          <div class="final-total">\n            <div>${n("finalTotalLabel","المجموع النهائي:")}</div>\n            <div style="font-size: 20px; margin-top: 2mm;">${Gt(e.finalTotal)}</div>\n          </div>\n        </div>\n\n        <div class="footer">\n          <div class="thank-you">${n("thankYouMessage","شكراً لزيارتكم")}</div>\n          <div class="date-time">${n("printedAtLabel","طُبعت في:")} ${(new Date).toLocaleString("ar"===N?"ar-DZ":"fr"===N?"fr-FR":"en-US")}</div>\n\n          <div class="developer-footer">\n            <div class="developer-name">Developed by iCode DZ</div>\n            <div class="developer-phone">0551930589</div>\n          </div>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=400,height=700");s.document.write(a),s.document.close(),s.onload=function(){setTimeout((()=>{s.print(),s.close()}),1e3)}},pn=e=>{if(L&&f.isThermalPrinterAvailable())return void hn(e);const t=window.open("","_blank","width=800,height=600");if(!t)return void Jt(`❌ ${n("popupBlocked","تم حظر النافذة المنبثقة - يرجى السماح بالنوافذ المنبثقة")}`,"error",5e3);const a="ar"===N,s=a?"left":"right",r=`\n      <!DOCTYPE html>\n      <html dir="${a?"rtl":"ltr"}" lang="${N}">\n      <head>\n        <meta charset="UTF-8">\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">\n        <title>${n("salesInvoiceTitle","فاتورة مبيعات")} - ${e.invoiceNumber}</title>\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            direction: ${a?"rtl":"ltr"};\n            background: white;\n            color: #333;\n            line-height: 1.6;\n            padding: 20px;\n          }\n\n          .invoice-container {\n            max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            border: 2px solid #ddd;\n            border-radius: 10px;\n            overflow: hidden;\n          }\n\n          .invoice-header {\n            background: linear-gradient(135deg, #007bff, #0056b3);\n            color: white;\n            padding: 30px;\n            text-align: center;\n            position: relative;\n          }\n\n          .header-logo {\n            position: absolute;\n            top: 20px;\n            ${s}: 30px;\n            width: 60px;\n            height: 60px;\n            background: ${Et.storeLogo?"white":"rgba(255, 255, 255, 0.2)"};\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-size: 18px;\n            font-weight: bold;\n            border: 2px solid rgba(255, 255, 255, 0.3);\n            overflow: hidden;\n          }\n\n          .header-logo img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n            border-radius: 50%;\n          }\n\n          .header-logo-fallback {\n            color: white;\n            font-size: 18px;\n            font-weight: bold;\n          }\n\n          .invoice-header h1 {\n            font-size: 2.5rem;\n            margin-bottom: 10px;\n            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n          }\n\n          .invoice-header p {\n            font-size: 1.1rem;\n            opacity: 0.9;\n          }\n\n          .invoice-info {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 30px;\n            padding: 30px;\n            background: #f8f9fa;\n            border-bottom: 3px solid #007bff;\n          }\n\n          .info-section h3 {\n            color: #007bff;\n            margin-bottom: 15px;\n            font-size: 1.3rem;\n            border-bottom: 2px solid #007bff;\n            padding-bottom: 5px;\n          }\n\n          .info-item {\n            margin-bottom: 8px;\n            font-size: 1.1rem;\n          }\n\n          .info-item strong {\n            color: #2c3e50;\n            display: inline-block;\n            min-width: 120px;\n          }\n\n          .items-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin: 30px 0;\n          }\n\n          .items-table th {\n            background: #007bff;\n            color: white;\n            padding: 15px 10px;\n            text-align: center;\n            font-size: 1.2rem;\n            font-weight: 600;\n          }\n\n          .items-table td {\n            padding: 12px 10px;\n            text-align: center;\n            border-bottom: 1px solid #ddd;\n            font-size: 1.1rem;\n            font-weight: 500;\n          }\n\n          .items-table tbody tr:nth-child(even) {\n            background: #f8f9fa;\n          }\n\n          .items-table tbody tr:hover {\n            background: #e3f2fd;\n          }\n\n          .totals-section {\n            background: #f8f9fa;\n            padding: 30px;\n            border-top: 3px solid #007bff;\n          }\n\n          .totals-grid {\n            display: grid;\n            grid-template-columns: 1fr 300px;\n            gap: 30px;\n            align-items: start;\n          }\n\n          .payment-info h3 {\n            color: #007bff;\n            margin-bottom: 15px;\n            font-size: 1.3rem;\n          }\n\n          .payment-method {\n            display: inline-block;\n            padding: 8px 16px;\n            border-radius: 20px;\n            font-weight: 600;\n            font-size: 1.1rem;\n          }\n\n          .payment-method.cash {\n            background: #d4edda;\n            color: #155724;\n            border: 2px solid #c3e6cb;\n          }\n\n          .payment-method.credit {\n            background: #fff3cd;\n            color: #856404;\n            border: 2px solid #ffeaa7;\n          }\n\n          .totals-table {\n            width: 100%;\n            border-collapse: collapse;\n          }\n\n          .totals-table td {\n            padding: 12px 15px;\n            border-bottom: 1px solid #ddd;\n            font-size: 1.2rem;\n          }\n\n          .totals-table .label {\n            font-weight: 600;\n            color: #2c3e50;\n            text-align: right;\n            background: #f8f9fa;\n          }\n\n          .totals-table .value {\n            font-weight: 700;\n            text-align: left;\n            color: #007bff;\n          }\n\n          .final-total {\n            background: #007bff !important;\n            color: white !important;\n            font-size: 1.4rem !important;\n            font-weight: 800 !important;\n          }\n\n          .invoice-footer {\n            background: #2c3e50;\n            color: white;\n            text-align: center;\n            padding: 20px;\n            font-size: 1.1rem;\n          }\n\n          .thank-you {\n            font-size: 1.3rem;\n            margin-bottom: 10px;\n            font-weight: 600;\n          }\n\n          @media print {\n            body {\n              padding: 0;\n            }\n\n            .invoice-container {\n              border: none;\n              border-radius: 0;\n              box-shadow: none;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="invoice-container">\n          \x3c!-- Invoice Header --\x3e\n          <div class="invoice-header">\n            <div class="header-logo">\n              ${Et.storeLogo?`<img src="${Et.storeLogo}" alt="${Et.storeName}" />`:'<div class="header-logo-fallback">iC</div>'}\n            </div>\n            <h1>${Et.storeName}</h1>\n            <p>📞 ${Et.storePhone} | 📍 ${Et.storeAddress}</p>\n          </div>\n\n          \x3c!-- Invoice Info --\x3e\n          <div class="invoice-info">\n            <div class="info-section">\n              <h3>📄 ${n("invoiceInfo","معلومات الفاتورة")}</h3>\n              <div class="info-item"><strong>${n("invoiceNumber","رقم الفاتورة")}:</strong> ${e.invoiceNumber}</div>\n              <div class="info-item"><strong>${n("date","التاريخ")}:</strong> ${e.date}</div>\n              <div class="info-item"><strong>${n("creationTime","وقت الإنشاء")}:</strong> ${e.createdAt||(new Date).toLocaleString("ar"===N?"ar-DZ":"fr"===N?"fr-FR":"en-US")}</div>\n            </div>\n\n            <div class="info-section">\n              <h3>👤 ${n("customerInfo","معلومات الزبون")}</h3>\n              <div class="info-item"><strong>${n("customerName","اسم الزبون")}:</strong> ${e.customerName||n("walkInCustomer","زبون عابر")}</div>\n              <div class="info-item"><strong>${n("paymentMethod","طريقة الدفع")}:</strong>\n                <span class="payment-method ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                  ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("cash","نقداً"):"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          \x3c!-- Items Table --\x3e\n          <table class="items-table">\n            <thead>\n              <tr>\n                <th>${n("product","المنتج")}</th>\n                <th>${n("quantity","الكمية")}</th>\n                <th>${n("price","السعر")}</th>\n                <th>${n("total","المجموع")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${e.items.map((e=>`\n                <tr>\n                  <td style="font-weight: 600; color: #2c3e50;">${e.productName}</td>\n                  <td style="font-weight: 600; color: #e74c3c;">${e.quantity}</td>\n                  <td style="font-weight: 600; color: #3498db;">${Gt(e.price)}</td>\n                  <td style="font-weight: 700; color: #27ae60;">${Gt(e.total)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n\n          \x3c!-- Totals Section --\x3e\n          <div class="totals-section">\n            <div class="totals-grid">\n              <div class="payment-info">\n                <h3>💳 ${n("paymentInfo","معلومات الدفع")}</h3>\n                <p><strong>${n("paymentMethod","طريقة الدفع")}:</strong>\n                  <span class="payment-method ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                    ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("cash","نقداً"):"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}\n                  </span>\n                </p>\n                <p><strong>${n("invoiceStatus","حالة الفاتورة")}:</strong>\n                  <span style="color: ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"#28a745":"#ffc107"}; font-weight: 600;">\n                    ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paidStatus","مدفوعة"):n("debtStatus","دين")}\n                  </span>\n                </p>\n              </div>\n\n              <table class="totals-table">\n                <tr>\n                  <td class="label">${n("subtotalLabel","المجموع الفرعي:")}</td>\n                  <td class="value">${Gt(e.total)}</td>\n                </tr>\n                <tr>\n                  <td class="label">${n("discountLabel","الخصم:")}</td>\n                  <td class="value">${Gt(e.discount||0)}</td>\n                </tr>\n                <tr>\n                  <td class="label">${n("taxLabel","الضريبة")} (${Et.taxRate}%):</td>\n                  <td class="value">${Gt(e.tax)}</td>\n                </tr>\n                <tr>\n                  <td class="label final-total">${n("finalTotalLabel","المجموع النهائي:")}</td>\n                  <td class="value final-total">${Gt(e.finalTotal)}</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          \x3c!-- Invoice Footer --\x3e\n          <div class="invoice-footer">\n            <div class="thank-you">${n("thankYouForDealingWithUs","شكراً لتعاملكم معنا")}</div>\n            <div>© ${(new Date).getFullYear()} ${Et.storeName} - ${n("allRightsReserved","جميع الحقوق محفوظة")}</div>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;t.document.write(r),t.document.close(),t.onload=function(){setTimeout((()=>{t.focus(),t.print(),L&&setTimeout((()=>t.close()),2e3)}),500)},Jt(`🖨️ ${n("printWindowOpened","تم فتح نافذة الطباعة")}`,"success",2e3)},xn=()=>{Ze(!1),_e(null)},gn=()=>{console.log("✏️ Closing Edit Invoice Modal - Clearing all edit states"),Jn(!1),Hn(null),Yn([]),_a(""),clearTimeout(window.editScannerValidationTimeout),da(""),ha(1),pa(0),console.log("✏️ Edit Invoice Modal closed - All states cleared")},vn=e=>{ea(e),na(e.items.map((e=>({...e,returnQuantity:0,maxReturnQuantity:e.quantity})))),Qn(!0)},bn=()=>{Qn(!1),ea(null),na([])},fn=()=>{ae(!0),Ke({invoiceNumber:"PUR-"+Date.now(),date:(new Date).toISOString().split("T")[0],supplierId:"",supplierName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0})},jn=()=>{ae(!1),We(null),da(""),ha(1),pa(0)},yn=()=>{if(!ca)return void Jt(`⚠️ ${n("pleaseSelectProduct","يرجى اختيار منتج")}`,"warning",3e3);if(ma<=0)return void Jt(`⚠️ ${n("pleaseEnterValidQuantity","يرجى إدخال كمية صحيحة")}`,"warning",3e3);if(ua<=0)return void Jt(`⚠️ ${n("pleaseEnterValidPrice","يرجى إدخال سعر صحيح")}`,"warning",3e3);const e=Mn.find((e=>e.id===ca));if(!e)return void Jt(`❌ ${n("productNotFoundInSystem","المنتج غير موجود")}`,"error",3e3);const t=Ye.items.findIndex((t=>t.productId===e.id));if(-1!==t){const e=[...Ye.items];e[t].quantity+=ma,e[t].total=e[t].quantity*ua;const n=e.reduce(((e,t)=>e+t.total),0),a=n*(Et.taxRate/100),s=n+a-Ye.discount;Ke({...Ye,items:e,total:n,tax:a,finalTotal:s})}else{const t={productId:e.id,productName:e.name,price:ua,quantity:ma,total:ua*ma},n=[...Ye.items,t],a=n.reduce(((e,t)=>e+t.total),0),s=a*(Et.taxRate/100),r=a+s-Ye.discount;Ke({...Ye,items:n,total:a,tax:s,finalTotal:r})}da(""),ha(1),pa(0),Jt(`✅ تم إضافة ${e.name} لفاتورة المشتريات`,"success",2e3)},Nn=()=>{if(0===Ye.items.length)return void Jt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);if(!Ye.supplierId)return void Jt(`⚠️ ${n("pleaseSelectSupplier","يرجى اختيار مورد")}`,"warning",3e3);const e=Ye.supplierName||n("generalSupplier","مورد عام");if(He){const t={...He,...Ye,customerName:e,updatedAt:(new Date).toISOString()},n={};He.items.forEach((e=>{n[e.productId]||(n[e.productId]=0),n[e.productId]-=e.quantity})),Ye.items.forEach((e=>{n[e.productId]||(n[e.productId]=0),n[e.productId]+=e.quantity}));const a=Mn.map((e=>n[e.id]?{...e,stock:e.stock+n[e.id]}:e)),s=et.map((e=>e.id===He.id?t:e));Tn(a),localStorage.setItem("icaldz-products",JSON.stringify(a)),rn(s),Jt(`✅ تم تحديث فاتورة المشتريات رقم ${Ye.invoiceNumber} وتحديث المخزون!`,"success",4e3)}else{const t=[{id:"PUR-"+Date.now(),...Ye,customerName:e,createdAt:(new Date).toISOString(),status:"نقداً"===Ye.paymentMethod||"Espèces"===Ye.paymentMethod||"En espèces"===Ye.paymentMethod||"Cash"===Ye.paymentMethod?"مدفوعة":"دين"},...et];rn(t),Ye.items.forEach((e=>{const t=Mn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Mn];n[t].stock+=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}})),Jt(`💾 تم حفظ فاتورة المشتريات رقم ${Ye.invoiceNumber} للمورد ${e} بنجاح وتم تحديث المخزون!`,"success",4e3)}jn()},wn=()=>{jt(!1)},$n=()=>{Lt(!1),qt({id:"",name:"",username:"",password:"",phone:"",email:"",role:"seller",isActive:!0})},Sn=e=>{localStorage.setItem("icaldz-categories",JSON.stringify(e)),Ln(e)},kn=()=>{Bn(!0),qn(""),Vn(null)},Cn=()=>{Bn(!1),qn(""),Vn(null)},Dn=()=>{if(!Un.trim())return void Jt(`⚠️ ${n("pleaseEnterCategoryName","يرجى إدخال اسم الفئة")}`,"warning");if(Fn.includes(Un.trim()))return void Jt(`❌ ${n("categoryAlreadyExists","الفئة موجودة مسبقاً")}`,"error");const e=[...Fn,Un.trim()];Sn(e),Jt(`✅ ${n("categoryAddedSuccessfully","تم إضافة الفئة")} "${Un.trim()}" ${n("successfully","بنجاح")}`,"success"),qn("")},In=(e,t)=>{if(!t.trim())return void Jt("⚠️ يرجى إدخال اسم الفئة الجديد","warning");if(Fn.includes(t.trim())&&t.trim()!==e)return void Jt("❌ الفئة موجودة مسبقاً","error");const n=Fn.map((n=>n===e?t.trim():n));Sn(n);const a=Mn.map((n=>n.category===e?{...n,category:t.trim()}:n));En(a),Jt(`✅ تم تحديث الفئة من "${e}" إلى "${t.trim()}" بنجاح`,"success"),Vn(null)};e.useEffect((()=>{Xt(),en(),nn(),sn(),Ln((()=>{const e=localStorage.getItem("icaldz-categories");if(e)try{return JSON.parse(e)}catch(n){console.error("خطأ في تحليل بيانات الفئات المحفوظة:",n)}const t=["إلكترونيات","ملابس","طعام ومشروبات","أدوات منزلية","كتب وقرطاسية","رياضة وترفيه","صحة وجمال","أطفال ولعب"];return localStorage.setItem("icaldz-categories",JSON.stringify(t)),t})())}),[]),e.useEffect((()=>{const e=setTimeout((()=>{An()}),1e3);return()=>clearTimeout(e)}),[]);const[Mn,Tn]=e.useState((()=>{const e=localStorage.getItem("icaldz-products");if(e)try{return JSON.parse(e).map((e=>({...e,buyPrice:parseFloat(e.buyPrice)||0,sellPrice:parseFloat(e.sellPrice||e.price)||0,price:parseFloat(e.sellPrice||e.price)||0})))}catch(t){console.error("خطأ في تحليل بيانات المنتجات المحفوظة:",t)}return[{id:"P001",name:"لابتوب HP",buyPrice:75e3,sellPrice:85e3,price:85e3,stock:15,barcode:"000000001",category:"إلكترونيات",minStock:5,createdAt:(new Date).toISOString()},{id:"P002",name:"ماوس لاسلكي",buyPrice:2e3,sellPrice:2500,price:2500,stock:50,barcode:"000000002",category:"إلكترونيات",minStock:10,createdAt:(new Date).toISOString()},{id:"P003",name:"كيبورد ميكانيكي",buyPrice:7e3,sellPrice:8500,price:8500,stock:25,barcode:"000000003",category:"إلكترونيات",minStock:5,createdAt:(new Date).toISOString()},{id:"P004",name:"شاشة 24 بوصة",buyPrice:38e3,sellPrice:45e3,price:45e3,stock:10,barcode:"000000004",category:"إلكترونيات",minStock:3,createdAt:(new Date).toISOString()},{id:"P005",name:"سماعات بلوتوث",buyPrice:9500,sellPrice:12e3,price:12e3,stock:30,barcode:"000000005",category:"إلكترونيات",minStock:8,createdAt:(new Date).toISOString()},{id:"P006",name:"كابل USB-C",buyPrice:800,sellPrice:1200,price:1200,stock:100,barcode:"000000006",category:"إلكترونيات",minStock:20,createdAt:(new Date).toISOString()},{id:"P007",name:"شاحن هاتف",buyPrice:1500,sellPrice:2200,price:2200,stock:40,barcode:"000000007",category:"إلكترونيات",minStock:10,createdAt:(new Date).toISOString()}]})),Pn=c({savedInvoices:Ie,products:Mn,expenses:at,formatPrice:Gt,showToast:Jt}),En=e=>{const t=e.map((e=>({...e,buyPrice:e.buyPrice||(e.price?.7*e.price:0),sellPrice:e.sellPrice||e.price||0,price:e.sellPrice||e.price||0,stock:e.stock||0,minStock:e.minStock||5,createdAt:e.createdAt||(new Date).toISOString()})));localStorage.setItem("icaldz-products",JSON.stringify(t)),Tn(t)},An=()=>{let e=!1;const t=Mn.map((t=>{const n={...t};if(!n.buyPrice||isNaN(parseFloat(n.buyPrice))||parseFloat(n.buyPrice)<=0){const t=parseFloat(n.sellPrice||n.price)||0;t>0&&(n.buyPrice=.7*t,e=!0,console.log(`تم إصلاح سعر الشراء للمنتج ${n.name}: ${n.buyPrice}`))}if(!n.sellPrice||isNaN(parseFloat(n.sellPrice))||parseFloat(n.sellPrice)<=0){const t=parseFloat(n.price)||0;t>0&&(n.sellPrice=t,e=!0,console.log(`تم إصلاح سعر البيع للمنتج ${n.name}: ${n.sellPrice}`))}return n.price&&parseFloat(n.price)===parseFloat(n.sellPrice)||(n.price=n.sellPrice,e=!0),n.stock&&!isNaN(parseInt(n.stock))||(n.stock=0,e=!0),n.minStock&&!isNaN(parseInt(n.minStock))||(n.minStock=5,e=!0),n.createdAt||(n.createdAt=(new Date).toISOString(),e=!0),n}));return e&&(console.log("تم إصلاح بيانات المنتجات وحفظها"),En(t),Jt("✅ تم إصلاح بيانات المنتجات تلقائياً","info",3e3)),t},[Rn,On]=e.useState({id:"",name:"",category:"",barcode:"",buyPrice:0,sellPrice:0,stock:0,minStock:0}),[Fn,Ln]=e.useState([]),[zn,Bn]=e.useState(!1),[Un,qn]=e.useState(""),[Gn,Vn]=e.useState(null),[Zn,Jn]=e.useState(!1),[_n,Hn]=e.useState(null),[Wn,Yn]=e.useState([]);e.useEffect((()=>{Zn&&Ya.current&&setTimeout((()=>{Ya.current.focus()}),100)}),[Zn]);const[Kn,Qn]=e.useState(!1),[Xn,ea]=e.useState(null),[ta,na]=e.useState([]),[aa,sa]=e.useState(!1),[ra,ia]=e.useState(""),[oa,la]=e.useState({invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"",customerName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0}),[ca,da]=e.useState(""),[ma,ha]=e.useState(1),[ua,pa]=e.useState(0),[xa,ga]=e.useState(""),va=[{id:1,title:n("totalSales","إجمالي المبيعات"),value:Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0)),unit:"",icon:"💰",color:"green"},{id:2,title:n("totalInvoices","عدد الفواتير"),value:Ie.length,unit:n("invoices","فاتورة"),icon:"📄",color:"blue"},{id:3,title:n("totalProducts","إجمالي المنتجات"),value:Mn.length,unit:n("products","منتج"),icon:"📦",color:"orange"},{id:4,title:n("lowStockProducts","منتجات منخفضة المخزون"),value:Mn.filter((e=>e.stock<=e.minStock)).length,unit:n("products","منتج"),icon:"⚠️",color:"red"}],ba=Ie.slice(0,5).map((e=>({id:e.invoiceNumber,date:e.date,supplier:Vt(e.customerName),paid:e.finalTotal,amount:e.finalTotal,status:n("paid","مدفوعة")}))),[fa,ja]=e.useState((()=>{const e=localStorage.getItem("icaldz-customers");return e?JSON.parse(e):[{id:"C001",name:"أحمد محمد",email:"<EMAIL>",phone:"0555123456",address:"الجزائر العاصمة",company:"شركة التجارة",balance:15e3,creditLimit:5e4,paymentTerm:30,discountPercentage:5,status:"نشط",createdAt:(new Date).toISOString()},{id:"C002",name:"فاطمة علي",email:"<EMAIL>",phone:"0555654321",address:"وهران",company:"مؤسسة النور",balance:-2500,creditLimit:3e4,paymentTerm:15,discountPercentage:3,status:"نشط",createdAt:(new Date).toISOString()},{id:"C003",name:"محمد حسن",email:"<EMAIL>",phone:"0555789012",address:"قسنطينة",company:"",balance:8750,creditLimit:2e4,paymentTerm:30,discountPercentage:7,status:"غير نشط",createdAt:(new Date).toISOString()},{id:"C004",name:"عائشة سالم",email:"<EMAIL>",phone:"0555345678",address:"عنابة",company:"متجر الأمل",balance:0,creditLimit:25e3,paymentTerm:30,discountPercentage:2,status:"نشط",createdAt:(new Date).toISOString()}]})),ya=e=>{localStorage.setItem("icaldz-customers",JSON.stringify(e)),ja(e)},[Na,wa]=e.useState({id:"",name:"",email:"",phone:"",address:"",company:"",balance:0,creditLimit:0,paymentTerm:30,discountPercentage:0,status:"نشط"}),[$a,Sa]=e.useState(!1),[ka,Ca]=e.useState(null),[Da,Ia]=e.useState(""),Ma=fa.filter((e=>e.name.toLowerCase().includes(Da.toLowerCase()))),[Ta,Pa]=e.useState(""),Ea=Ie.filter((e=>e.customerName?.toLowerCase().includes(Ta.toLowerCase())||e.invoiceNumber?.toLowerCase().includes(Ta.toLowerCase()))),[Aa,Ra]=e.useState(""),Oa=et.filter((e=>e.customerName?.toLowerCase().includes(Aa.toLowerCase())||e.invoiceNumber?.toLowerCase().includes(Aa.toLowerCase())||e.supplierName?.toLowerCase().includes(Aa.toLowerCase()))),Fa=e=>{xe(e),ve(e.balance),fe("نقداً"),ue(!0)},La=()=>{ue(!1),xe(null),ve(0),fe("نقداً")},za=(e,t,n)=>{const a=fa.map((a=>a.id===e?{...a,balance:"add"===n?a.balance+t:a.balance-t}:a));ya(a)},Ba=()=>{Sa(!1),Ca(null)},[Ua,qa]=e.useState(""),[Ga,Va]=e.useState(null),Za=e.useRef(null),[Ja,_a]=e.useState(""),[Ha,Wa]=e.useState(null),Ya=e.useRef(null);e.useState(null),e.useState(null);const Ka=()=>{console.log("🛒 Opening Sales Modal - Resetting all states for complete independence"),la({invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:n("cash","نقداً"),items:[],total:0,discount:0,tax:0,finalTotal:0}),qa(""),clearTimeout(window.salesScannerValidationTimeout),da(""),ha(1),pa(0),ga(""),_a(""),clearTimeout(window.editScannerValidationTimeout),Pe(""),console.log("🛒 All states cleared - Opening sales modal"),te(!0),b.play("newInvoice",{showNotification:!1})},Qa=()=>{if(console.log("🛒 Closing Sales Modal - Clearing all sales states"),oa.items.length>0)return console.log("⚠️ Warning: Closing modal with items in invoice"),void Jt(`⚠️ ${n("invoiceHasItems","الفاتورة تحتوي على منتجات - استخدم زر الحفظ أو احذف المنتجات")}`,"warning",4e3);te(!1),da(""),ha(1),pa(0),ga(""),qa(""),clearTimeout(window.salesScannerValidationTimeout),console.log("🛒 Sales Modal closed - All sales states cleared"),KeyboardShortcuts.setActiveWindow(J),b.play("closeWindow",{showNotification:!1})},Xa=()=>{console.log("🛒 Force Closing Sales Modal"),te(!1),da(""),ha(1),pa(0),ga(""),qa(""),clearTimeout(window.salesScannerValidationTimeout),console.log("🛒 Sales Modal force closed"),KeyboardShortcuts.setActiveWindow(J),b.play("closeWindow",{showNotification:!1})},es=()=>{console.log("🛒 Force Closing Sales Modal After Successful Save - Bypassing item check"),te(!1),da(""),ha(1),pa(0),ga(""),console.log("🔧 Resetting all scanner states to prevent conflicts"),Pe(""),qa(""),clearTimeout(window.salesScannerValidationTimeout),_a(""),clearTimeout(window.editScannerValidationTimeout),Va(null),Wa(null),console.log("🔧 All scanner states reset"),la({invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:n("cash","نقداً"),items:[],total:0,discount:0,tax:0,finalTotal:0}),console.log("🛒 Sales Modal closed after successful save - Ready for next invoice"),KeyboardShortcuts.setActiveWindow(J),b.play("closeWindow",{showNotification:!1}),setTimeout((()=>{"dashboard"===J&&Ee.current&&(Ee.current.focus(),console.log("🎯 Dashboard: Auto-focused barcode input after invoice save"))}),300)},ts=()=>{if(!ca)return void Jt(`⚠️ ${n("pleaseSelectProduct","يرجى اختيار منتج")}`,"warning",3e3);if(ma<=0)return void Jt(`⚠️ ${n("pleaseEnterValidQuantity","يرجى إدخال كمية صحيحة")}`,"warning",3e3);const e=Mn.find((e=>e.id===ca));if(!e)return void Jt(`❌ ${n("productNotFoundInSystem","المنتج غير موجود")}`,"error",3e3);if(ma>e.stock)return void Jt(`❌ ${n("quantityExceedsStock","الكمية المطلوبة أكبر من المتوفر")}: (${ma}) > (${e.stock})`,"error",3e3);const t={productId:e.id,productName:e.name,price:ua||e.sellPrice||e.price||0,quantity:ma,total:(ua||e.sellPrice||e.price||0)*ma},a=[...oa.items,t],s=a.reduce(((e,t)=>e+t.total),0),r=s*(Et.taxRate/100),i=s+r-oa.discount;la({...oa,items:a,total:s,tax:r,finalTotal:i}),da(""),ha(1),pa(0),b.play("addProduct",{showNotification:!1}),Jt(`✅ ${n("productAddedToInvoice","تم إضافة")} ${e.name} ${n("toInvoice","للفاتورة")}`,"success",2e3)},ns=Mn.filter((e=>{if(!xa)return!0;const t=xa.toLowerCase();return e.name.toLowerCase().includes(t)||e.id.toLowerCase().includes(t)||e.barcode&&e.barcode.toLowerCase().includes(t)})),as=e=>{const t=Mn.map((t=>{const n=e.find((e=>e.productId===t.id));return n?{...t,stock:t.stock-n.quantity}:t}));En(t)},ss=()=>{if(0===oa.items.length)return void Jt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);const e="دين"===oa.paymentMethod||"Crédit"===oa.paymentMethod||"Credit"===oa.paymentMethod,t="GUEST"===oa.customerId||!oa.customerId||"زبون عابر"===oa.customerName||"Client de passage"===oa.customerName||"Walk-in Customer"===oa.customerName;if(e&&t)return void Jt(`❌ ${n("cannotSaveCreditForWalkInCustomer","لا يمكن حفظ فاتورة دين لزبون عابر")}. ${n("pleaseSelectRegisteredCustomerForCredit","يرجى اختيار زبون مسجل للدفع بالدين")}.`,"error",4e3);if(e&&oa.customerId&&"GUEST"!==oa.customerId){const e=fa.find((e=>e.id===oa.customerId));if(e&&e.creditLimit>0){const t=e.balance+oa.finalTotal;if(t>e.creditLimit){const a=t-e.creditLimit;return void Jt(`❌ ${n("creditLimitExceeded","تم تجاوز حد الائتمان")}! ${n("customerName","العميل")}: ${e.name}\n${n("currentBalance","الرصيد الحالي")}: ${Gt(e.balance)}\n${n("invoiceAmount","مبلغ الفاتورة")}: ${Gt(oa.finalTotal)}\n${n("newBalance","الرصيد الجديد")}: ${Gt(t)}\n${n("creditLimit","حد الائتمان")}: ${Gt(e.creditLimit)}\n${n("exceededAmount","المبلغ المتجاوز")}: ${Gt(a)}`,"error",8e3)}}}for(const l of oa.items){const e=Mn.find((e=>e.id===l.productId));if(!e)return void Jt(`❌ ${n("productNotFoundInSystem","المنتج غير موجود")}: ${l.productName}`,"error",3e3);if(l.quantity>e.stock)return void Jt(`❌ ${n("quantityExceedsStock","الكمية المطلوبة أكبر من المتوفر")}: ${e.name}`,"error",3e3)}const a=oa.customerId||"GUEST",s=oa.customerName||n("walkInCustomer","زبون عابر"),r={...oa,customerId:a,customerName:s,id:oa.invoiceNumber,createdAt:(new Date).toLocaleString("ar-DZ")},i=[r,...Ie];localStorage.setItem("icaldz-invoices",JSON.stringify(i)),Me(i),as(oa.items),"دين"===oa.paymentMethod&&oa.customerId&&"GUEST"!==oa.customerId&&za(oa.customerId,oa.finalTotal,"add");const o=s===n("walkInCustomer","زبون عابر")?n("forWalkInCustomer","لزبون عابر"):`${n("forCustomer","للزبون")} ${s}`;b.play("saveInvoice",{showNotification:!1}),Jt(`💾 F2: ${n("salesInvoiceSavedSuccessfully","تم حفظ فاتورة المبيعات رقم")} ${oa.invoiceNumber} ${o} ${n("successfullyAndStockUpdated","بنجاح!")}`,"success",3e3),Ht(oa.finalTotal,oa.invoiceNumber),es(),L&&setTimeout((()=>{hn(r),b.play("printInvoice",{showNotification:!1}),Jt(`🖨️ F2: ${n("invoicePrintedAutomatically","تم طباعة الفاتورة تلقائياً")}`,"info",2e3)}),500)},rs=()=>{if(0===oa.items.length)return void Jt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);const e="دين"===oa.paymentMethod||"Crédit"===oa.paymentMethod||"Credit"===oa.paymentMethod,t="GUEST"===oa.customerId||!oa.customerId||"زبون عابر"===oa.customerName||"Client de passage"===oa.customerName||"Walk-in Customer"===oa.customerName;if(e&&t)return void Jt(`❌ ${n("cannotSaveCreditForWalkInCustomer","لا يمكن حفظ فاتورة دين لزبون عابر")}`,"error",4e3);if(e&&oa.customerId&&"GUEST"!==oa.customerId){const e=fa.find((e=>e.id===oa.customerId));if(e&&e.creditLimit>0){const t=e.balance+oa.finalTotal;if(t>e.creditLimit){const a=t-e.creditLimit;return void Jt(`❌ ${n("creditLimitExceeded","تم تجاوز حد الائتمان")}! ${n("customerName","العميل")}: ${e.name}\n${n("currentBalance","الرصيد الحالي")}: ${Gt(e.balance)}\n${n("invoiceAmount","مبلغ الفاتورة")}: ${Gt(oa.finalTotal)}\n${n("newBalance","الرصيد الجديد")}: ${Gt(t)}\n${n("creditLimit","حد الائتمان")}: ${Gt(e.creditLimit)}\n${n("exceededAmount","المبلغ المتجاوز")}: ${Gt(a)}`,"error",8e3)}}}const a="GUEST"===oa.customerId?n("walkInCustomer","زبون عابر"):oa.customerName,s=[{id:oa.invoiceNumber,invoiceNumber:oa.invoiceNumber,date:oa.date,customerId:oa.customerId,customerName:a,paymentMethod:oa.paymentMethod,items:oa.items,total:oa.total,discount:oa.discount,tax:oa.tax,finalTotal:oa.finalTotal,createdAt:(new Date).toISOString()},...Ie];localStorage.setItem("icaldz-invoices",JSON.stringify(s)),Me(s),as(oa.items),"دين"!==oa.paymentMethod&&"Crédit"!==oa.paymentMethod&&"Credit"!==oa.paymentMethod||!oa.customerId||"GUEST"===oa.customerId||za(oa.customerId,oa.finalTotal,"add");const r=a===n("walkInCustomer","زبون عابر")?n("forWalkInCustomer","لزبون عابر"):`${n("forCustomer","للزبون")} ${a}`;b.play("saveInvoice",{showNotification:!1}),Jt(`💾 ${n("salesInvoiceSavedSuccessfully","تم حفظ فاتورة المبيعات رقم")} ${oa.invoiceNumber} ${r} ${n("successfullyAndStockUpdated","بنجاح وتم تحديث المخزون!")}`,"success",4e3),Ht(oa.finalTotal,oa.invoiceNumber),es()},is=()=>{if(0===oa.items.length)return void Jt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);const e="دين"===oa.paymentMethod||"Crédit"===oa.paymentMethod||"Credit"===oa.paymentMethod,t="GUEST"===oa.customerId||!oa.customerId||"زبون عابر"===oa.customerName||"Client de passage"===oa.customerName||"Walk-in Customer"===oa.customerName;if(e&&t)return void Jt(`❌ ${n("cannotSaveCreditForWalkInCustomer","لا يمكن حفظ فاتورة دين لزبون عابر")}`,"error",4e3);if(e&&oa.customerId&&"GUEST"!==oa.customerId){const e=fa.find((e=>e.id===oa.customerId));if(e&&e.creditLimit>0){const t=e.balance+oa.finalTotal;if(t>e.creditLimit){const a=t-e.creditLimit;return void Jt(`❌ ${n("creditLimitExceeded","تم تجاوز حد الائتمان")}! ${n("customerName","العميل")}: ${e.name}\n${n("currentBalance","الرصيد الحالي")}: ${Gt(e.balance)}\n${n("invoiceAmount","مبلغ الفاتورة")}: ${Gt(oa.finalTotal)}\n${n("newBalance","الرصيد الجديد")}: ${Gt(t)}\n${n("creditLimit","حد الائتمان")}: ${Gt(e.creditLimit)}\n${n("exceededAmount","المبلغ المتجاوز")}: ${Gt(a)}`,"error",8e3)}}}const a="GUEST"===oa.customerId?n("walkInCustomer","زبون عابر"):oa.customerName,s={id:oa.invoiceNumber,invoiceNumber:oa.invoiceNumber,date:oa.date,customerId:oa.customerId,customerName:a,paymentMethod:oa.paymentMethod,items:oa.items,total:oa.total,discount:oa.discount,tax:oa.tax,finalTotal:oa.finalTotal,createdAt:(new Date).toISOString()},r=[s,...Ie];localStorage.setItem("icaldz-invoices",JSON.stringify(r)),Me(r),as(oa.items),"دين"!==oa.paymentMethod&&"Crédit"!==oa.paymentMethod&&"Credit"!==oa.paymentMethod||!oa.customerId||"GUEST"===oa.customerId||za(oa.customerId,oa.finalTotal,"add");const i=a===n("walkInCustomer","زبون عابر")?n("forWalkInCustomer","لزبون عابر"):`${n("forCustomer","للزبون")} ${a}`;b.play("saveInvoice",{showNotification:!1}),Jt(`💾 ${n("salesInvoiceSavedSuccessfully","تم حفظ فاتورة المبيعات رقم")} ${oa.invoiceNumber} ${i} ${n("successfullyAndStockUpdated","بنجاح وتم تحديث المخزون!")}`,"success",4e3),Ht(oa.finalTotal,oa.invoiceNumber),setTimeout((()=>{hn(s),b.play("printInvoice",{showNotification:!1}),Jt(`🖨️ ${n("invoiceSentToThermalPrinter","تم إرسال الفاتورة للطباعة الحرارية")}`,"info",3e3)}),500),es()},os=e=>{console.log("🛒 Sales: Adding product by scanner code:",e);const t=Mn.find((t=>t.barcode===e&&""!==e.trim()));if(!t)return Jt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${e}`,"error",2e3),qa(""),void Va(null);const a=oa.items.findIndex((e=>e.productId===t.id));let s;if(a>=0)s=[...oa.items],s[a]={...s[a],quantity:s[a].quantity+1,total:(s[a].quantity+1)*s[a].price};else{const e={productId:t.id,productName:t.name,name:t.name,barcode:t.barcode,price:t.sellPrice||t.salePrice||t.price,quantity:1,total:t.sellPrice||t.salePrice||t.price};s=[...oa.items,e]}const r=s.reduce(((e,t)=>e+t.total),0),i=r*(Et.taxRate/100),o=r+i-oa.discount;la({...oa,items:s,total:r,tax:i,finalTotal:o}),qa(""),clearTimeout(window.salesScannerValidationTimeout),b.play("addProduct"),console.log("🛒 Sales: Product added successfully:",t.name)},ls=e=>{if(!Zn||ee)return void console.log("🚫 Edit scanner BLOCKED - edit modal not open or sales modal is open");const t=e.target.value,a=$(t);if(_a(a),console.log("✏️ Edit scanner input:",{raw:t,cleaned:a}),a.length>=3){const e=Mn.find((e=>e.barcode===a&&""!==a.trim()));e?(console.log("✏️ Edit: Auto-adding product to editing invoice:",e.name),ds(a)):console.log("✏️ Edit: Product not found for code:",a)}else a.length>0&&a.length<3&&(clearTimeout(window.editScannerValidationTimeout),window.editScannerValidationTimeout=setTimeout((()=>{Ja===a&&a.length>0&&a.length<3&&Jt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}),1e3))},cs=e=>{if(Zn&&!ee){if("Enter"===e.key&&"keydown"===e.type){e.preventDefault(),e.stopPropagation();const t=e.target.value.trim();console.log("✏️ Edit Enter pressed with code:",t),t.length>=3?(_a(""),clearTimeout(window.editScannerValidationTimeout)):t.length>0&&Jt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}}else console.log("🚫 Edit scanner keypress BLOCKED - edit modal not open or sales modal is open")},ds=e=>{if(!_n)return void Jt(`❌ ${n("noInvoiceBeingEdited","لا توجد فاتورة قيد التعديل")}`,"error",2e3);console.log("✏️ Edit: Adding product by scanner code:",e);const t=Mn.find((t=>t.barcode===e&&""!==e.trim()));if(!t)return Jt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${e}`,"error",2e3),_a(""),void Wa(null);if(t.stock<=0)return Jt(`❌ ${n("productOutOfStock","المنتج غير متوفر في المخزون")}: ${t.name}`,"error",3e3),_a(""),void Wa(null);const a=_n.items.findIndex((e=>e.productId===t.id));let s;if(a>=0){const e=_n.items[a],r=e.quantity+1;if(r>t.stock)return Jt(`❌ ${n("quantityExceedsStock","الكمية الإجمالية أكبر من المتوفر")} (${t.stock})`,"error",3e3),void _a("");s=[..._n.items],s[a]={...e,quantity:r,total:r*e.price},Jt(`✅ ${n("quantityUpdated","تم تحديث الكمية")}: ${t.name} (${r})`,"success",2e3)}else{const e={id:Date.now(),productId:t.id,productName:t.name,name:t.name,price:t.sellPrice||t.price||0,quantity:1,total:t.sellPrice||t.price||0};s=[..._n.items,e],Jt(`✅ ${n("productAdded","تم إضافة المنتج")}: ${t.name}`,"success",2e3)}const r=s.reduce(((e,t)=>e+t.total),0),i=r*(Et.taxRate/100),o=r+i-_n.discount;Hn({..._n,items:s,total:r,tax:i,finalTotal:o}),_a(""),clearTimeout(window.editScannerValidationTimeout),b.play("addProduct"),console.log("✏️ Edit: Product added successfully:",t.name)},ms=()=>{const e=Mn.map((e=>e.barcode)).filter((e=>e&&/^0{6}\d{3}$/.test(e))).map((e=>parseInt(e,10))).filter((e=>!isNaN(e))).sort(((e,t)=>t-e)),t=e.length>0?e[0]+1:6;return String(t).padStart(9,"0")},hs=()=>{sa(!0),On({id:"P"+String(Date.now()).slice(-3),name:"",category:"",barcode:"",buyPrice:0,sellPrice:0,stock:0,minStock:0})},us=()=>{sa(!1),On({id:"",name:"",category:"",barcode:"",buyPrice:0,sellPrice:0,stock:0,minStock:0})},ps=()=>{if(!Rn.name||!Rn.category)return void Jt(`⚠️ ${n("fillRequiredFields","يرجى ملء جميع الحقول المطلوبة")}`,"warning",3e3);const e=Rn.name.trim();if(Mn.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==Rn.id)))return void Jt(`❌ ${n("productNameAlreadyExists","اسم المنتج موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);let t=Rn.barcode;if(t&&""!==t.trim()||(t=ms(),Jt(`🔢 ${n("autoBarcodeGenerated","تم توليد باركود تلقائي")}: ${t}`,"info",2e3)),t&&""!==t.trim()){const e=Mn.find((e=>e.barcode===t&&e.id!==Rn.id));if(e)return void Jt(`❌ ${n("barcodeAlreadyExists","الباركود موجود بالفعل")}: "${t}" - ${n("usedByProduct","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const a=Mn.find((e=>e.barcode===t));if(a){const e={...Rn,id:a.id,barcode:t,price:Rn.sellPrice,createdAt:a.createdAt,updatedAt:(new Date).toISOString()},s=Mn.map((t=>t.id===a.id?e:t));En(s),Jt(`✅ ${n("productUpdatedSuccessfully","تم تحديث المنتج")} ${Rn.name} ${n("successfully","بنجاح")}`,"success",3e3)}else{const e={...Rn,barcode:t,price:Rn.sellPrice,createdAt:(new Date).toISOString()},a=[...Mn,e];En(a),Rn.barcode&&""!==Rn.barcode.trim()?Jt(`✅ ${n("productAddedWithScannedBarcode","تم إضافة المنتج")} ${Rn.name} ${n("withScannedBarcode","بالباركود المسحوب:")} ${t}`,"success",3e3):Jt(`✅ ${n("productAddedWithAutoBarcode","تم إضافة المنتج")} ${Rn.name} ${n("withAutoBarcode","بباركود تلقائي:")} ${t}`,"success",3e3)}us()},xs=(e,t)=>{if("مدير"!==zt.role&&"admin"!==zt.role)return void Jt(`❌ ${n("notAllowedManagerOnly","غير مسموح - المدير فقط يمكنه تعديل المخزون")}`,"error");const a=Mn.map((n=>n.id===e?{...n,stock:t}:n));En(a),Jt(`✅ ${n("stockQuantityUpdated","تم تحديث الكمية بنجاح")}`,"success",2e3)},gs=e=>{On({...e}),sa(!0)},vs=Mn.filter((e=>{const t=e.name.toLowerCase().includes(ra.toLowerCase())||e.id.toLowerCase().includes(ra.toLowerCase())||e.barcode.includes(ra),n=""===je||e.category===je;let a=!0;if(""!==Ne){a=(e.stock<=e.minStock?"منخفض":0===e.stock?"نفد":e.stock>2*e.minStock?"مرتفع":"عادي")===Ne}return t&&n&&a})),bs=e=>{e.preventDefault();const t=localStorage.getItem("icaldz-sellers");let a=[];t?a=JSON.parse(t):(a=[{id:"S001",name:"مدير النظام",username:"admin",password:"admin",phone:"+213 555 000 000",email:"<EMAIL>",role:"admin",isActive:!0,createdAt:(new Date).toISOString()}],localStorage.setItem("icaldz-sellers",JSON.stringify(a)),Ot(a));const s=a.find((e=>e.username===ke.username&&e.password===ke.password&&e.isActive));if(s)W(!0),_("dashboard"),Bt(s),De(!0,"dashboard"),Jt(`🎉 ${n("welcomeUser","مرحباً بك")} ${s.name}!`,"success",3e3);else{a.find((e=>e.username===ke.username&&e.password===ke.password&&!e.isActive))?Jt(`❌ ${n("accountInactiveContactManager","حسابك غير نشط، يرجى التواصل مع المدير")}`,"error",4e3):Jt(`❌ ${n("invalidUsernameOrPassword","اسم المستخدم أو كلمة المرور غير صحيحة")}`,"error",3e3)}},fs=e=>{_(e),H&&De(!0,e)},js=()=>{K(null),X(!1)};return e.useEffect((()=>{const e=e=>{if(O){if((e.key.startsWith("F")||"Escape"===e.key)&&(e.preventDefault(),e.stopPropagation()),"F1"===e.key)return console.log("F1 pressed - New Sales Invoice (shortcuts enabled)"),Ka(),void Jt(`🛒 F1: ${n("f1NewSalesInvoiceOpened","تم فتح فاتورة مبيعات جديدة")}`,"success",2e3);if("F6"===e.key)return console.log("F6 pressed - New Product in Inventory (shortcuts enabled)"),void("inventory"===J?(hs(),Jt(`📦 F6: ${n("f6NewProductOpened","تم فتح نافذة إضافة منتج جديد")}`,"success",2e3)):(_("inventory"),setTimeout((()=>{hs(),Jt(`📦 F6: ${n("f6NavigatedToInventory","تم الانتقال لإدارة المخزون وفتح نافذة إضافة منتج جديد")}`,"success",2e3)}),100)));if("F7"===e.key)return console.log("F7 pressed - New Purchase Invoice (shortcuts enabled)"),void("purchases"===J?(fn(),Jt(`🛒 F7: ${n("f7NewPurchaseInvoiceOpened","تم فتح فاتورة مشتريات جديدة")}`,"success",2e3)):(_("purchases"),setTimeout((()=>{fn(),Jt(`🛒 F7: ${n("f7NavigatedToPurchases","تم الانتقال لإدارة المشتريات وفتح فاتورة مشتريات جديدة")}`,"success",2e3)}),100)));if("F2"===e.key)return console.log("F2 pressed - Open new sales invoice (shortcuts enabled)"),void(ee||aa||ne||(Ka(),Jt(`🛒 F2: ${n("f2NewInvoiceOpened","تم فتح فاتورة جديدة")}`,"success",2e3)));if("F3"===e.key)return console.log("F3 pressed - Add Product (shortcuts enabled)"),void(ee?(ts(),Jt(`➕ F3: ${n("f3ProductAdded","تم إضافة المنتج")}`,"success",2e3)):ne?(yn(),Jt(`➕ F3: ${n("f3ProductAddedToPurchase","تم إضافة المنتج للمشتريات")}`,"success",2e3)):Jt(`⚠️ F3 ${n("f3OnlyAvailableInInvoice","متاح فقط في نافذة الفاتورة")}`,"warning",2e3));if("Enter"===e.key){console.log("Enter pressed - Save action (shortcuts enabled)");if("dashboard"===J&&document.activeElement&&document.activeElement.classList.contains("barcode-input"))return void console.log("Enter in dashboard barcode input - ignoring global handler");return document.activeElement&&(document.activeElement.classList.contains("barcode-input")||document.activeElement.classList.contains("barcode-input-field"))?void console.log("Enter in barcode input field - ignoring global handler"):void(ee?e.shiftKey?(is(),Jt(`🖨️ ${n("shiftEnterSaveAndPrint","Shift+Enter: تم حفظ الفاتورة وإرسالها للطباعة الحرارية")}`,"success",3e3)):(rs(),Jt(`💾 ${n("enterSaveOnly","Enter: تم حفظ الفاتورة")}`,"success",2e3)):aa?(ps(),Jt("✅ Enter: تم حفظ المنتج","success",2e3)):ne&&(Nn(),Jt("💾 Enter: تم حفظ فاتورة المشتريات","success",2e3)))}"Escape"!==e.key||(ee?(Xa(),Jt("❌ تم إغلاق نافذة الفاتورة","info",2e3)):$a?(Ba(),Jt("❌ تم إغلاق نافذة تعديل الزبون","info",2e3)):aa?(us(),Jt("❌ تم إغلاق نافذة المنتج","info",2e3)):Q?(js(),Jt("❌ تم إغلاق نافذة الزبون","info",2e3)):ne&&(jn(),Jt("❌ تم إغلاق نافذة المشتريات","info",2e3)))}};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[O,ee,aa,Q,$a,ne,J,ca,oa,ss,rs,is,ts,yn,ps,Nn,Ka,hs,fn,Qa,us,js,Ba,jn,Jt]),a?H?t.jsxs("div",{className:"accounting-system",children:[t.jsxs("div",{className:`sidebar page-${J}`,children:[t.jsxs("div",{className:"sidebar-header",children:[t.jsxs("div",{className:"system-logo",children:[t.jsx("img",{src:"./assets/logo2png.png",alt:"نظام المحاسبي",className:"logo-image",onError:e=>{console.log("Logo failed to load, showing fallback"),e.target.style.display="none",e.target.nextSibling.style.display="block"}}),t.jsx("div",{className:"logo-fallback",style:{display:"none"},children:"🏛️"})]}),t.jsxs("div",{className:"system-title",children:[t.jsx("h3",{children:n("systemName","نظام المحاسبي")}),t.jsxs("span",{children:[n("version","الإصدار")," 1.0"]})]})]}),t.jsxs("nav",{className:"sidebar-nav",children:[t.jsxs("button",{className:"nav-item "+("dashboard"===J?"active":""),onClick:()=>fs("dashboard"),children:[t.jsx("span",{className:"nav-icon",children:"🏠"}),n("dashboard","لوحة التحكم")]}),t.jsxs("button",{className:"nav-item "+("sales"===J?"active":""),onClick:()=>fs("sales"),children:[t.jsx("span",{className:"nav-icon",children:"🛒"}),n("sales","المبيعات")]}),t.jsxs("button",{className:"nav-item "+("purchases"===J?"active":""),onClick:()=>fs("purchases"),children:[t.jsx("span",{className:"nav-icon",children:"📦"}),n("purchases","المشتريات")]}),t.jsxs("button",{className:"nav-item "+("customers"===J?"active":""),onClick:()=>fs("customers"),children:[t.jsx("span",{className:"nav-icon",children:"👥"}),n("customers","العملاء")]}),t.jsxs("button",{className:"nav-item "+("inventory"===J?"active":""),onClick:()=>fs("inventory"),children:[t.jsx("span",{className:"nav-icon",children:"📊"}),n("products","المخزون")]}),t.jsxs("button",{className:"nav-item "+("reports"===J?"active":""),onClick:()=>fs("reports"),children:[t.jsx("span",{className:"nav-icon",children:"📈"}),n("reports","التقارير")]}),t.jsxs("button",{className:"nav-item "+("settings"===J?"active":""),onClick:()=>fs("settings"),children:[t.jsx("span",{className:"nav-icon",children:"⚙️"}),n("settings","الإعدادات")]})]}),t.jsxs("div",{className:"sidebar-footer",children:[t.jsxs("div",{className:"user-info "+("ar"!==N?"user-info-left":""),children:[t.jsx("div",{className:"user-avatar",children:"👤"}),t.jsxs("div",{className:"user-details",children:[t.jsx("span",{children:"المدير"===zt.name?n("manager","المدير"):zt.name||n("systemUser","مستخدم النظام")}),t.jsx("small",{children:"admin"===zt.role||"مدير"===zt.role?n("systemManager","مدير النظام"):n("seller","بائع")})]})]}),t.jsxs("div",{className:"system-controls",children:[t.jsx("button",{className:"control-btn "+(A?"active":"inactive"),onClick:()=>{const e=b.toggle();R(e),Jt(e?`🔊 ${n("soundEnabled","تم تفعيل الأصوات")}`:`🔇 ${n("soundDisabled","تم إيقاف الأصوات")}`,"info",2e3)},title:A?n("disableSounds","إيقاف الأصوات"):n("enableSounds","تفعيل الأصوات"),children:A?"🔊":"🔇"}),t.jsx("button",{className:"control-btn "+(O?"active":"inactive"),onClick:()=>{const e=!O;F(e),localStorage.setItem("shortcutsEnabled",JSON.stringify(e)),Jt(e?`⌨️ ${n("keyboardShortcutsEnabled","تم تفعيل اختصارات لوحة المفاتيح")}`:`🚫 ${n("keyboardShortcutsDisabled","تم إيقاف اختصارات لوحة المفاتيح")}`,"info",3e3)},title:O?n("disableKeyboardShortcuts","إيقاف اختصارات لوحة المفاتيح"):n("enableKeyboardShortcuts","تفعيل اختصارات لوحة المفاتيح"),children:O?"⌨️":"🚫"}),t.jsx("button",{className:"control-btn "+(L?"active":"inactive"),onClick:()=>{const e=!L;z(e),localStorage.setItem("printerEnabled",JSON.stringify(e)),Jt(e?`🖨️ ${n("printerEnabled","تم تفعيل الطابعة")}`:`🚫 ${n("printerDisabled","تم إيقاف الطابعة")}`,"info",2e3)},title:L?n("disablePrinter","إيقاف الطابعة"):n("enablePrinter","تفعيل الطابعة"),children:L?"🖨️":"🚫"}),t.jsx("button",{className:"control-btn "+(B?"active":"inactive"),onClick:()=>{const e=!B;U(e),localStorage.setItem("notificationsEnabled",JSON.stringify(e)),Jt(e?`🔔 ${n("notificationsEnabled","تم تفعيل الإشعارات")}`:`🔕 ${n("notificationsDisabled","تم إيقاف الإشعارات")}`,"info",2e3)},title:B?n("disableNotifications","إيقاف الإشعارات"):n("enableNotifications","تفعيل الإشعارات"),children:B?"🔔":"🔕"})]}),t.jsxs("button",{className:"logout-btn",onClick:()=>{W(!1),_("login"),Ce({username:"",password:""}),De(!1,"login")},children:["🚪 ",n("logout","خروج")]})]})]}),t.jsxs("main",{className:"main-content",children:["dashboard"===J&&t.jsxs("div",{className:"dashboard",children:[t.jsx("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["🏠 ",n("dashboard","لوحة التحكم")]})})}),t.jsx("div",{className:"dashboard-scanner-lcd-unified",children:t.jsxs("div",{className:"unified-frame",children:[t.jsxs("div",{className:"scanner-section",children:[t.jsxs("h3",{children:["📷 ",n("scanBarcode","مسح الباركود")," - ",t.jsx("span",{className:"scanner-status-active",children:n("active","نشط")})]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanBarcodeToAddProduct","امسح الباركود - اضغط Enter لفتح الفاتورة"),value:Te,onChange:e=>{if(ee||Zn)return void console.log("🚫 Dashboard scanner BLOCKED - modal is open");const t=e.target.value,n=$(t);if(Pe(n),console.log("🏠 Enhanced dashboard scanner input:",{raw:t,cleaned:n,productsCount:Mn.length,currentPage:J,scannerTool:"Enhanced for barcode tools"}),n.length>=3&&(clearTimeout(window.dashboardScannerTimeout),window.dashboardScannerTimeout=setTimeout((()=>{const e=Mn.find((e=>e.barcode===n&&""!==n.trim()));e&&(console.log("🏠 Dashboard: Auto-detected product from scanner tool:",{productName:e.name,barcode:e.barcode,scannedCode:n}),Re({productName:e.name,price:e.sellPrice,barcode:e.barcode,showPrice:!0}))}),100)),n.length>0&&Oe&&(console.log("💰 Dashboard LCD: Clearing total display - new barcode scan started"),Fe(null),Le&&(clearTimeout(Le),ze(null))),n.length>=3){const e=Mn.find((e=>e.barcode===n&&""!==n.trim()));if(e)console.log("🏠 Dashboard: Product found, ready for Enter to open invoice:",{productName:e.name,barcode:e.barcode,scannedCode:n});else{console.log("🏠 Dashboard: Product not found for code:",n);const e=Mn.map((e=>e.barcode)).filter((e=>e));console.log("🏠 Available barcodes:",e.slice(0,5))}}},onKeyDown:e=>{if(ee||Zn)console.log("🚫 Dashboard scanner keypress BLOCKED - modal is open");else if("Enter"===e.key&&"keydown"===e.type){e.preventDefault(),e.stopPropagation();const t=e.target.value.trim();if(console.log("🏠 Dashboard Enter pressed with code:",{code:t,length:t.length,productsAvailable:Mn.length}),t.length>=3){const e=Mn.find((e=>e.barcode===t&&""!==t.trim()));if(e){console.log("🏠 Dashboard: Opening sales invoice with product:",{name:e.name,barcode:e.barcode,id:e.id}),Pe("");const t={invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:n("cash","نقداً"),items:[],total:0,discount:0,tax:0,finalTotal:0};qa(""),_a(""),la(t),te(!0),setTimeout((()=>{console.log("🏠 Dashboard: Adding product to new sales invoice:",e.name);const n={productId:e.id,productName:e.name,name:e.name,quantity:1,price:e.sellPrice||e.price,total:e.sellPrice||e.price},a=n.total,s=a*(Et.taxRate/100),r=a+s;la({...t,items:[n],total:a,tax:s,finalTotal:r}),console.log("🏠 Dashboard: Product added to new invoice:",e.name)}),300),Jt(`🛒 ${n("openingSalesInvoice","فتح فاتورة المبيعات")} - ${e.name}`,"success",2e3)}else console.log("🏠 Dashboard: Product not found. Available barcodes:",Mn.map((e=>e.barcode)).filter((e=>e)).slice(0,10)),Jt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${t}`,"error",3e3)}else Jt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}},onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1),console.log("🔧 BARCODE FIX: Dashboard scanner focused - shortcuts disabled"))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Dashboard scanner blurred - shortcuts re-enabled"))}),100)},className:"barcode-input",ref:Ee,autoFocus:!0})]}),t.jsxs("div",{className:"barcode-actions",children:[t.jsxs("button",{type:"button",className:"btn btn-success btn-sm",onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("🏠 Dashboard: Manual button click - Opening invoice");const t=Te.trim();if(t&&S(t)){const e=Mn.find((e=>e.barcode===t&&""!==t.trim()));if(e){console.log("🏠 Dashboard: Opening sales invoice with product:",e.name),Pe("");const t={invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0};qa(""),la(t),te(!0),setTimeout((()=>{console.log("🏠 Dashboard: Adding product to new sales invoice:",e.name);const n={productId:e.id,productName:e.name,quantity:1,price:e.sellPrice||e.price,total:e.sellPrice||e.price},a=n.total,s=a*(Et.taxRate/100),r=a+s;la({...t,items:[n],total:a,tax:s,finalTotal:r}),console.log("🏠 Dashboard: Product added to new invoice:",e.name)}),300),Jt(`🛒 ${n("openingSalesInvoice","فتح فاتورة المبيعات")} - ${e.name}`,"success",2e3)}else Jt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${t}`,"error",2e3)}else Jt(`⚠️ ${n("pleaseEnterBarcode","يرجى إدخال الباركود أولاً")}`,"warning",2e3)},title:n("openInvoiceWithProduct","فتح الفاتورة مع المنتج"),children:["🛒 ",n("openInvoice","فتح الفاتورة")]}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{Pe("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]}),t.jsx("button",{type:"button",className:"btn btn-info btn-sm",onClick:()=>{console.log("🔍 BARCODE SCANNING DEBUG INFO:"),console.log("📊 Products count:",Mn.length),console.log("📋 Available barcodes:",Mn.map((e=>({name:e.name,barcode:e.barcode}))).slice(0,10)),console.log("🏠 Dashboard scanner input:",Te),console.log("🛒 Sales scanner input:",Ua),console.log("✏️ Edit scanner input:",Ja),console.log("🔄 Modal states:",{showSalesModal:ee,showEditInvoiceModal:Zn,showProductModal:aa,currentPage:J});console.log("🧪 Testing barcode validation:"),["000000001","123456789","ABC123","test123","12","a"].forEach((e=>{const t=S(e),n=$(e);console.log(`  ${e} -> Valid: ${t}, Sanitized: "${n}"`)})),Jt("🔍 Debug info logged to console - Press F12 to view","info",3e3)},title:"Debug barcode scanning",children:"🔍 Debug"}),t.jsxs("button",{type:"button",className:"btn btn-danger btn-sm",onClick:I,title:n("resetScannerSystem","إعادة تعيين نظام الباركود"),children:["🔧 ",n("reset","إعادة تعيين")]})]}),t.jsxs("small",{className:"barcode-help",style:{color:"#7fb3d3",marginTop:"10px",display:"block",textAlign:"center",fontSize:"12px",fontStyle:"italic"},children:["💡 ",n("dashboardBarcodeHelp","امسح الباركود لعرض المعلومات، اضغط Enter أو زر فتح الفاتورة لإضافة المنتج للفاتورة")]})]}),t.jsx("div",{className:"lcd-section",children:t.jsxs("div",{className:"lcd-screen",children:[t.jsxs("div",{className:"lcd-header",children:[t.jsxs("span",{className:"lcd-title",children:["💰 ",n("totalFinal","المجموع النهائي")]}),t.jsx("span",{className:"lcd-status",children:Oe||Be?"●":"○"})]}),t.jsx("div",{className:"lcd-content",children:Oe?t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Gt(Oe.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"}),t.jsxs("div",{className:"invoice-info",children:[t.jsx("div",{className:"invoice-number",children:Oe.invoiceNumber}),t.jsx("div",{className:"timestamp",children:Oe.timestamp})]})]}):Be?t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("lastInvoice","آخر فاتورة"),":"]}),t.jsx("div",{className:"total-final-amount",children:Gt(Be.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"}),t.jsxs("div",{className:"invoice-info",children:[t.jsx("div",{className:"invoice-number",children:Be.invoiceNumber}),t.jsxs("div",{className:"timestamp",children:[Be.date," - ",Be.timestamp]})]})]}):t.jsxs("div",{className:"display-placeholder",children:[t.jsx("div",{className:"placeholder-text",children:n("scanBarcodeToOpenInvoice","امسح الباركود واضغط Enter لفتح الفاتورة")}),t.jsxs("div",{className:"placeholder-price-big",children:["--- ",Et.currency]}),t.jsxs("div",{className:"placeholder-scanner-hint",children:["📷 ",n("scannerActive","الماسح نشط")]})]})})]})})]})}),t.jsxs("div",{className:"dashboard-action-buttons",children:[t.jsxs("button",{className:"action-btn action-btn-sales",onClick:Ka,title:"F1",children:[t.jsx("div",{className:"btn-shortcut",children:"F1"}),t.jsx("div",{className:"btn-icon",children:"🛒"}),t.jsx("div",{className:"btn-text",children:n("newSalesInvoice","فاتورة مبيعات جديدة")})]}),t.jsxs("button",{className:"action-btn action-btn-product",onClick:hs,title:"F6",children:[t.jsx("div",{className:"btn-shortcut",children:"F6"}),t.jsx("div",{className:"btn-icon",children:"📦"}),t.jsx("div",{className:"btn-text",children:n("addNewProduct","إضافة منتج جديد")})]}),t.jsxs("button",{className:"action-btn action-btn-purchase",onClick:()=>{_("purchases"),setTimeout((()=>{fn()}),100)},title:"F7",children:[t.jsx("div",{className:"btn-shortcut",children:"F7"}),t.jsx("div",{className:"btn-icon",children:"📦"}),t.jsx("div",{className:"btn-text",children:n("newPurchaseInvoice","فاتورة مشتريات جديدة")})]})]}),t.jsx("div",{className:"stats-grid",children:va.map((e=>t.jsxs("div",{className:`stat-card ${e.color}`,children:[t.jsx("div",{className:"stat-icon",children:e.icon}),t.jsxs("div",{className:"stat-content",children:[t.jsx("h3",{children:e.title}),t.jsxs("div",{className:"stat-value",children:[e.value," ",t.jsx("span",{className:"stat-unit",children:e.unit})]})]})]},e.id)))}),t.jsxs("div",{className:"dashboard-section",children:[t.jsx("h2",{children:n("recentInvoicesAndOperations","آخر الفواتير والعمليات")}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("supplier","المورد")}),t.jsx("th",{children:n("amountPaid","المبلغ المدفوع")}),t.jsx("th",{children:n("totalAmount","المبلغ الإجمالي")}),t.jsx("th",{children:n("status","الحالة")})]})}),t.jsx("tbody",{children:ba.map((e=>t.jsxs("tr",{children:[t.jsx("td",{children:e.id}),t.jsx("td",{children:e.date}),t.jsx("td",{children:Vt(e.supplier)}),t.jsx("td",{children:Gt(e.paid)}),t.jsx("td",{children:Gt(e.amount)}),t.jsx("td",{children:t.jsx("span",{className:"status "+("مدفوعة"===e.status?"paid":"partial"),children:"مدفوعة"===e.status?n("paid","مدفوعة"):e.status})})]},e.id)))})]})}),t.jsxs("div",{className:"actions-header",children:[t.jsx("h3",{children:n("quickOperations","العمليات السريعة")}),t.jsx("p",{children:n("chooseOperation","اختر العملية التي تريد تنفيذها")})]}),t.jsxs("div",{className:"table-actions",children:[t.jsxs("button",{className:"btn btn-success btn-large quick-operation-btn",onClick:Ka,title:n("newSalesInvoice","إنشاء فاتورة بيع جديدة"),children:[t.jsx("span",{className:"btn-icon",children:"🛒"}),t.jsx("span",{className:"btn-text",children:n("newSalesInvoice","فاتورة مبيعات جديدة")})]}),t.jsxs("button",{className:"btn btn-primary quick-operation-btn",onClick:()=>{_("purchases"),setTimeout((()=>{fn()}),100),Jt(`📄 ${n("f7NewPurchaseInvoiceOpened","تم فتح فاتورة مشتريات جديدة")}`,"success",2e3)},title:n("newPurchaseInvoice","إنشاء فاتورة مشتريات جديدة"),children:[t.jsx("span",{className:"btn-icon",children:"📄"}),t.jsx("span",{className:"btn-text",children:n("newPurchaseInvoice","فاتورة مشتريات جديدة")})]}),t.jsxs("button",{className:"btn btn-secondary quick-operation-btn",onClick:()=>{_("purchases"),Jt(`📊 ${n("purchaseReportClicked","تم الانتقال لصفحة المشتريات")}`,"success",2e3)},title:n("purchaseReport","عرض تقرير المشتريات"),children:[t.jsx("span",{className:"btn-icon",children:"📊"}),t.jsx("span",{className:"btn-text",children:n("purchaseReport","تقرير المشتريات")})]}),t.jsxs("button",{className:"btn btn-info quick-operation-btn",onClick:()=>{"مدير"===zt.role||"admin"===zt.role?(_("reports"),Jt(`📈 ${n("purchaseStatisticsClicked","تم الانتقال لصفحة التقارير والإحصائيات")}`,"success",2e3)):Jt(`🔒 ${n("reportsManagerOnly","التقارير والإحصائيات متاحة للمدير فقط")}`,"warning",3e3)},title:n("purchaseStatistics","عرض إحصائيات المشتريات"),children:[t.jsx("span",{className:"btn-icon",children:"📈"}),t.jsx("span",{className:"btn-text",children:n("purchaseStatistics","إحصائيات المشتريات")})]})]})]})]}),"customers"===J&&t.jsxs("div",{className:"customers-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["👥 ",n("customersManagement","إدارة الزبائن")]})}),t.jsx("div",{className:"page-description-section",children:t.jsxs("button",{className:"btn btn-primary",onClick:()=>X(!0),children:["+ ",n("addNewCustomer","إضافة زبون جديد")]})})]}),t.jsxs("div",{className:"stats-grid",children:[t.jsxs("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-icon",children:t.jsx("i",{className:"fas fa-users"})}),t.jsxs("div",{className:"stat-info",children:[t.jsx("h3",{children:n("totalCustomers","إجمالي الزبائن")}),t.jsx("p",{children:fa.length})]})]}),t.jsxs("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-icon",children:t.jsx("i",{className:"fas fa-money-bill-wave"})}),t.jsxs("div",{className:"stat-info",children:[t.jsx("h3",{children:n("totalDues","إجمالي المستحقات")}),t.jsxs("p",{children:[fa.reduce(((e,t)=>e+Math.max(0,t.balance)),0).toLocaleString()," ",Et.currency]})]})]}),t.jsxs("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-icon",children:t.jsx("i",{className:"fas fa-exclamation-triangle"})}),t.jsxs("div",{className:"stat-info",children:[t.jsx("h3",{children:n("debtorCustomers","زبائن مدينين")}),t.jsx("p",{children:fa.filter((e=>e.balance>0)).length})]})]})]}),fa.length>0&&t.jsxs("div",{className:"bulk-actions "+(St.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:fa.length>0&&St.length===fa.length,onChange:()=>Wt("customers",fa),id:"select-all-customers"}),t.jsx("label",{htmlFor:"select-all-customers",className:"select-all-label",children:n("selectAll","تحديد الكل")}),St.length>0&&t.jsxs("span",{className:"selected-count",children:["(",St.length," ",n("selected","محدد"),")"]})]}),St.length>0&&("مدير"===zt.role||"admin"===zt.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Kt("customers"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",St.length,")"]})]}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",className:"search-input",placeholder:`🔍 ${n("searchCustomers","البحث في الزبائن (الاسم، الهاتف، البريد الإلكتروني)...")}`,value:Da,onChange:e=>Ia(e.target.value)}),Da&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>Ia(""),title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==N?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:fa.length>0&&St.length===fa.length,onChange:()=>Wt("customers",fa),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("customerNumber","رقم الزبون")}),t.jsx("th",{children:n("customerName","اسم الزبون")}),t.jsx("th",{children:n("phone","الهاتف")}),t.jsx("th",{children:n("email","البريد الإلكتروني")}),t.jsx("th",{children:n("company","الشركة")}),t.jsx("th",{children:n("balance","الرصيد")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("profitMarginDiscount","خصم (%)")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Ma.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"10",style:{textAlign:"center",padding:"20px"},children:Da.trim()?`${n("noCustomersMatchingFilter","لا توجد زبائن مطابقين للفلتر")}: "${Da}"`:n("noCustomersAdded","لا توجد زبائن مضافين")})}):Ma.map((e=>t.jsxs("tr",{className:St.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:St.includes(e.id),onChange:()=>Yt("customers",e.id)})}),t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:e.email}),t.jsx("td",{children:e.company||"-"}),t.jsxs("td",{className:e.balance<=0?"text-success":"text-danger",children:[e.balance.toLocaleString()," ",Et.currency]}),t.jsx("td",{children:t.jsx("span",{className:"status-badge "+("نشط"===e.status?"active":"inactive"),children:"نشط"===e.status?n("active","نشط"):n("inactive","غير نشط")})}),t.jsx("td",{children:t.jsxs("span",{className:"discount-badge",children:[e.discountPercentage||0,"%"]})}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-info btn-xs",onClick:()=>(e=>{console.log("🔍 DEBUG: Customer selected:",e),console.log("🔍 DEBUG: All savedInvoices:",Ie);const t=Ie.filter((t=>{console.log("🔍 DEBUG: Checking invoice:",{invoiceNumber:t.invoiceNumber,customerName:t.customerName,customerId:t.customerId,customer:t.customer,targetCustomerName:e.name,targetCustomerId:e.id});const n=t.customerName===e.name,a=t.customerId===e.id,s=t.customer===e.name,r=t.customerName&&t.customerName.toLowerCase().trim()===e.name.toLowerCase().trim(),i=n||a||s||r;return console.log("🔍 DEBUG: Invoice matches:",i),i})),n=JSON.parse(localStorage.getItem("icaldz-payments")||"[]").filter((t=>{const n=t.customerId===e.id,a=t.customerName===e.name,s=t.customerName&&t.customerName.toLowerCase().trim()===e.name.toLowerCase().trim();return n||a||s}));console.log("🔍 DEBUG: Filtered customer invoices:",t),console.log("🔍 DEBUG: Filtered customer payments:",n);const a=[...t.map((e=>({...e,type:"invoice",operationType:"sale"}))),...n.map((e=>({...e,type:"payment",operationType:"payment",invoiceNumber:e.id,finalTotal:-e.amount,paymentMethod:e.paymentMethod})))].sort(((e,t)=>new Date(t.date)-new Date(e.date))),s=a.length,r=t.reduce(((e,t)=>e+t.finalTotal),0),i=n.reduce(((e,t)=>e+t.amount),0),o=t.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)),l=t.filter((e=>"دين"===e.paymentMethod||"Dette"===e.paymentMethod||"Credit"===e.paymentMethod)),c=o.reduce(((e,t)=>e+t.finalTotal),0),d=l.reduce(((e,t)=>e+t.finalTotal),0),m={customer:e,invoices:t,payments:n,allOperations:a,totalOperations:s,totalAmount:r,totalPayments:i,cashOperations:o,creditOperations:l,totalCash:c,totalCredit:d};console.log("🔍 DEBUG: Final operations data (customer-specific only):",m),console.log("🔍 DEBUG: Customer operations count:",a.length),console.log("🔍 DEBUG: Customer invoices count:",t.length),console.log("🔍 DEBUG: Customer payments count:",n.length),oe(m),re(!0)})(e),title:n("viewCustomerOperations","عرض عمليات الزبون"),children:"👁️"}),e.balance>0&&t.jsx("button",{className:"btn btn-success btn-xs",onClick:()=>Fa(e),title:n("payCustomerDebt","تسديد فواتير الزبون"),children:"💰"}),t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>(e=>{Ca({...e}),Sa(!0)})(e),title:n("editCustomer","تعديل الزبون"),children:"✏️"}),("مدير"===zt.role||"admin"===zt.role)&&t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{const t=fa.find((t=>t.id===e));me(t),ce(!0)})(e.id),title:n("deleteCustomer","حذف الزبون"),children:"🗑️"})]})})]},e.id)))})]})})]}),"purchases"===J&&t.jsxs("div",{className:"purchases-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["📦 ",n("purchaseManagement","إدارة المشتريات")]})}),t.jsx("div",{className:"page-description-section",children:t.jsxs("button",{className:"btn btn-primary",onClick:fn,children:["📦 ",n("newPurchaseInvoice","فاتورة مشتريات جديدة")]})})]}),t.jsxs("div",{className:"purchase-summary",children:[t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("totalPurchases","إجمالي المشتريات")}),t.jsx("div",{className:"summary-value",children:Gt(et.reduce(((e,t)=>e+t.finalTotal),0))})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:n("purchaseInvoiceCount","عدد فواتير المشتريات")}),t.jsx("div",{className:"summary-value",children:et.length})]}),t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("averagePurchaseInvoice","متوسط فاتورة المشتريات")}),t.jsx("div",{className:"summary-value",children:et.length>0?Gt(et.reduce(((e,t)=>e+t.finalTotal),0)/et.length):Gt(0)})]}),t.jsxs("div",{className:"summary-card purple",children:[t.jsx("h3",{children:n("creditPurchaseInvoices","فواتير دين")}),t.jsx("div",{className:"summary-value",children:et.filter((e=>"دين"===e.paymentMethod||"Dette"===e.paymentMethod||"Credit"===e.paymentMethod)).length})]})]}),Oa.length>0&&t.jsxs("div",{className:"bulk-actions "+(wt.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:Oa.length>0&&wt.length===Oa.length,onChange:()=>Wt("purchases",Oa),id:"select-all-purchases"}),t.jsx("label",{htmlFor:"select-all-purchases",className:"select-all-label",children:n("selectAll","تحديد الكل")}),wt.length>0&&t.jsxs("span",{className:"selected-count",children:["(",wt.length," ",n("selected","محدد"),")"]})]}),wt.length>0&&("مدير"===zt.role||"admin"===zt.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Kt("purchases"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",wt.length,")"]})]}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",className:"search-input",placeholder:`🔍 ${n("searchPurchaseInvoices","البحث في فواتير المشتريات (رقم الفاتورة، اسم المورد)...")}`,value:Aa,onChange:e=>Ra(e.target.value)}),Aa&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>Ra(""),title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:Oa.length>0&&wt.length===Oa.length,onChange:()=>Wt("purchases",Oa),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("supplier","المورد")}),t.jsx("th",{children:n("paymentMethod","طريقة الدفع")}),t.jsx("th",{children:n("amount","المبلغ")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Oa.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"8",style:{textAlign:"center",padding:"20px"},children:Aa.trim()?`${n("noPurchaseInvoicesMatchingFilter","لا توجد فواتير مشتريات مطابقة للفلتر")}: "${Aa}"`:n("noPurchaseInvoicesSaved","لا توجد فواتير مشتريات محفوظة")})}):Oa.map((e=>t.jsxs("tr",{className:wt.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:wt.includes(e.id),onChange:()=>Yt("purchases",e.id)})}),t.jsx("td",{children:e.invoiceNumber}),t.jsx("td",{children:e.date}),t.jsx("td",{children:Zt(e.customerName)}),t.jsx("td",{children:t.jsx("span",{className:"payment-method "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"),children:"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")})}),t.jsx("td",{children:Gt(e.finalTotal)}),t.jsx("td",{children:t.jsx("span",{className:"status "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"paid":"pending"),children:"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")})}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-info btn-xs",onClick:()=>mn(e),title:n("viewInvoiceDetails","عرض تفاصيل الفاتورة"),children:"👁️"}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>pn(e),title:n("print","طباعة"),children:"🖨️"}),("مدير"===zt.role||"admin"===zt.role)&&t.jsxs(t.Fragment,{children:[t.jsx("button",{className:"btn btn-warning btn-xs",onClick:()=>(e=>{"مدير"===zt.role||"admin"===zt.role?(Ke({...e,invoiceNumber:e.invoiceNumber,date:e.date,supplierId:e.supplierId||"",supplierName:e.customerName||e.supplierName||"",paymentMethod:e.paymentMethod||"نقداً",items:[...e.items],total:e.total,discount:e.discount||0,tax:e.tax,finalTotal:e.finalTotal}),We(e),ae(!0),Jt(`✏️ ${n("purchaseInvoiceOpenedForEdit","تم فتح فاتورة المشتريات للتعديل")}`,"info")):Jt(`❌ ${n("notAllowedManagerOnlyPurchase","غير مسموح - المدير فقط يمكنه تعديل فواتير المشتريات")}`,"error")})(e),title:n("editPurchaseInvoice","تعديل فاتورة المشتريات (المدير فقط)"),children:"✏️"}),t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{if("مدير"===zt.role||"admin"===zt.role){if(window.confirm(`${n("confirmDeletePurchaseInvoice","هل أنت متأكد من حذف فاتورة المشتريات")} ${e.invoiceNumber}؟`)){const t=JSON.parse(localStorage.getItem("icaldz-purchases")||"[]").filter((t=>t.id!==e.id));localStorage.setItem("icaldz-purchases",JSON.stringify(t)),e.items.forEach((e=>{const t=Mn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Mn];n[t].stock-=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}})),Jt(`🗑️ ${n("purchaseInvoiceDeletedAndStockAdjusted","تم حذف فاتورة المشتريات وإعادة تعديل المخزون")}`,"success")}}else Jt(`❌ ${n("notAllowedManagerOnlyDeletePurchase","غير مسموح - المدير فقط يمكنه حذف فواتير المشتريات")}`,"error")})(e),title:n("deletePurchaseInvoice","حذف فاتورة المشتريات (المدير فقط)"),children:"🗑️"})]})]})})]},e.id)))})]})}),t.jsxs("div",{className:"suppliers-section",children:[t.jsxs("div",{className:"section-header "+("ar"!==N?"section-header-ltr":""),children:[t.jsxs("h2",{children:["🏭 ",n("suppliersManagement","إدارة الموردين")]}),t.jsxs("button",{className:"btn btn-success",onClick:on,children:["➕ ",n("addNewSupplierButton","إضافة مورد جديد")]})]}),t.jsx("div",{className:"suppliers-table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==N?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("supplierID","رقم المورد")}),t.jsx("th",{children:n("supplierNameHeader","اسم المورد")}),t.jsx("th",{children:n("supplierPhoneHeader","رقم الهاتف")}),t.jsx("th",{children:n("supplierEmailHeader","البريد الإلكتروني")}),t.jsx("th",{children:n("supplierAddressHeader","العنوان")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Qe.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:n("noSuppliersAdded","لا توجد موردين مضافين")})}):Qe.map((e=>t.jsxs("tr",{children:[t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:e.email}),t.jsx("td",{children:e.address}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>(e=>{gt({...e}),pt(!0)})(e),title:n("editSupplier","تعديل المورد"),children:"✏️"}),t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>cn(e.id),title:n("deleteSupplier","حذف المورد"),children:"🗑️"})]})})]},e.id)))})]})})]})]}),"sales"===J&&t.jsxs("div",{className:"sales-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["🛒 ",n("salesManagement","إدارة المبيعات")]})}),t.jsx("div",{className:"page-description-section",children:t.jsx("div",{className:"header-actions",children:t.jsxs("button",{className:"btn btn-info btn-compact",onClick:()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=n("salesReport","تقرير المبيعات"),s=n("reportDate","تاريخ التقرير"),r=n("generationTime","وقت الإنشاء"),i=n("salesSummary","ملخص المبيعات"),o=n("totalSales","إجمالي المبيعات"),l=n("invoiceCount","عدد الفواتير"),c=n("averageInvoice","متوسط الفاتورة"),d=n("cashSales","المبيعات النقدية"),m=n("salesInvoiceDetails","تفاصيل فواتير المبيعات"),h=n("invoiceNumber","رقم الفاتورة"),u=n("date","التاريخ"),p=n("customer","الزبون"),x=n("paymentMethod","طريقة الدفع"),g=n("subtotal","المبلغ الفرعي"),v=n("tax","الضريبة"),b=n("discount","الخصم"),f=n("finalAmount","المبلغ النهائي"),j=n("status","الحالة"),y=n("noSalesInvoicesToDisplay","لا توجد فواتير مبيعات لعرضها"),w=n("walkInCustomer","زبون عابر"),$=n("cash","نقداً"),S=n("credit","دين"),k=n("paid","مدفوعة"),C=n("additionalStatistics","إحصائيات إضافية"),D=n("totalTaxes","إجمالي الضرائب"),I=n("totalDiscounts","إجمالي الخصومات"),M=n("creditInvoices","فواتير الدين"),T=n("creditValue","قيمة الديون"),P=n("accountingSystemIntegratedBusinessManagement","نظام المحاسبي - إدارة الأعمال المتكاملة"),E=n("reportGeneratedAutomatically","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ"),A=n("allRightsReserved","جميع الحقوق محفوظة"),R=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${a} - ${Et.storeName}</title>\n        <style>\n          @page {\n            size: A4;\n            margin: 20mm;\n          }\n\n          body {\n            font-family: 'Arial', sans-serif;\n            direction: ${e?"rtl":"ltr"};\n            margin: 0;\n            padding: 0;\n            color: #333;\n            line-height: 1.6;\n          }\n\n          .header {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            margin-bottom: 30px;\n            padding-bottom: 20px;\n            border-bottom: 3px solid #007bff;\n          }\n\n          .logo-section {\n            display: flex;\n            align-items: center;\n            gap: 15px;\n          }\n\n          .logo {\n            width: 80px;\n            height: 80px;\n            background: linear-gradient(135deg, #007bff, #0056b3);\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-size: 24px;\n            font-weight: bold;\n            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);\n          }\n\n          .company-info h1 {\n            margin: 0;\n            color: #007bff;\n            font-size: 28px;\n            font-weight: bold;\n          }\n\n          .company-info p {\n            margin: 5px 0;\n            color: #666;\n            font-size: 14px;\n          }\n\n          .report-info {\n            text-align: ${e?"left":"right"};\n            direction: ${e?"ltr":"rtl"};\n          }\n\n          .report-title {\n            font-size: 24px;\n            font-weight: bold;\n            color: #007bff;\n            margin: 0;\n          }\n\n          .report-date {\n            color: #666;\n            font-size: 14px;\n            margin: 5px 0;\n          }\n\n          .summary-section {\n            margin: 30px 0;\n            background: #f8f9fa;\n            padding: 20px;\n            border-radius: 10px;\n            border-${e?"left":"right"}: 5px solid #007bff;\n          }\n\n          .store-logo {\n            width: 80px;\n            height: 80px;\n            border-radius: 50%;\n            object-fit: cover;\n            border: 3px solid #007bff;\n          }\n\n          .summary-title {\n            font-size: 20px;\n            font-weight: bold;\n            color: #007bff;\n            margin-bottom: 15px;\n          }\n\n          .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(4, 1fr);\n            gap: 20px;\n          }\n\n          .summary-card {\n            background: white;\n            padding: 15px;\n            border-radius: 8px;\n            text-align: center;\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n          }\n\n          .summary-card h3 {\n            margin: 0 0 10px 0;\n            font-size: 14px;\n            color: #666;\n          }\n\n          .summary-card .value {\n            font-size: 24px;\n            font-weight: bold;\n            color: #007bff;\n          }\n\n          .table-section {\n            margin: 30px 0;\n          }\n\n          .section-title {\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 15px;\n            padding-bottom: 10px;\n            border-bottom: 2px solid #e9ecef;\n          }\n\n          .data-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n            background: white;\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n            border-radius: 8px;\n            overflow: hidden;\n          }\n\n          .data-table th {\n            background: linear-gradient(135deg, #007bff, #0056b3);\n            color: white;\n            padding: 12px 8px;\n            text-align: center;\n            font-weight: bold;\n            font-size: 14px;\n          }\n\n          .data-table td {\n            padding: 10px 8px;\n            text-align: center;\n            border-bottom: 1px solid #e9ecef;\n            font-size: 13px;\n          }\n\n          .data-table tr:nth-child(even) {\n            background-color: #f8f9fa;\n          }\n\n          .data-table tr:hover {\n            background-color: #e3f2fd;\n          }\n\n          .amount {\n            font-weight: bold;\n            color: #28a745;\n          }\n\n          .status-paid {\n            background: #28a745;\n            color: white;\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-size: 11px;\n          }\n\n          .status-credit {\n            background: #ffc107;\n            color: #333;\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-size: 11px;\n          }\n\n          .footer {\n            margin-top: 40px;\n            padding-top: 20px;\n            border-top: 2px solid #e9ecef;\n            text-align: center;\n            color: #666;\n            font-size: 12px;\n          }\n\n          .footer-logo {\n            margin-bottom: 10px;\n          }\n\n          @media print {\n            body {\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n\n            .no-print {\n              display: none !important;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <div class="logo-section">\n            ${Et.logo?`\n              <img src="${Et.logo}" alt="Store Logo" class="store-logo">\n            `:'\n              <div class="logo">\n                iC\n              </div>\n            '}\n            <div class="company-info">\n              <h1>${Et.storeName}</h1>\n              <p>📞 ${Et.storePhone}</p>\n              <p>📍 ${Et.storeAddress}</p>\n            </div>\n          </div>\n          <div class="report-info">\n            <h2 class="report-title">${a}</h2>\n            <p class="report-date">${s}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n            <p class="report-date">${r}: ${(new Date).toLocaleTimeString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          </div>\n        </div>\n\n        <div class="summary-section">\n          <h2 class="summary-title">📊 ${i}</h2>\n          <div class="summary-grid">\n            <div class="summary-card">\n              <h3>${o}</h3>\n              <div class="value">${Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0))}</div>\n            </div>\n            <div class="summary-card">\n              <h3>${l}</h3>\n              <div class="value">${Ie.length}</div>\n            </div>\n            <div class="summary-card">\n              <h3>${c}</h3>\n              <div class="value">${Ie.length>0?Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0)/Ie.length):"0.00 دج"}</div>\n            </div>\n            <div class="summary-card">\n              <h3>${d}</h3>\n              <div class="value">${Gt(Ie.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="table-section">\n          <h2 class="section-title">📋 ${m}</h2>\n          <table class="data-table">\n            <thead>\n              <tr>\n                <th>${h}</th>\n                <th>${u}</th>\n                <th>${p}</th>\n                <th>${x}</th>\n                <th>${g}</th>\n                <th>${v}</th>\n                <th>${b}</th>\n                <th>${f}</th>\n                <th>${j}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${0===Ie.length?`\n                <tr>\n                  <td colspan="9" style="text-align: center; padding: 30px; color: #666;">\n                    ${y}\n                  </td>\n                </tr>\n              `:Ie.map((e=>`\n                <tr>\n                  <td style="font-weight: bold;">${e.invoiceNumber}</td>\n                  <td>${new Date(e.date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                  <td>${e.customerName||w}</td>\n                  <td>${"نقداً"===e.paymentMethod?$:"دين"===e.paymentMethod?S:"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?$:"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?S:e.paymentMethod||$}</td>\n                  <td class="amount">${Gt(e.total)}</td>\n                  <td class="amount">${Gt(e.tax)}</td>\n                  <td class="amount">${Gt(e.discount)}</td>\n                  <td class="amount" style="font-size: 14px; font-weight: bold;">${Gt(e.finalTotal)}</td>\n                  <td>\n                    <span class="${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"status-paid":"status-credit"}">\n                      ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?k:S}\n                    </span>\n                  </td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        ${Ie.length>0?`\n          <div class="table-section">\n            <h2 class="section-title">📈 ${C}</h2>\n            <div class="summary-grid">\n              <div class="summary-card">\n                <h3>${D}</h3>\n                <div class="value">${Gt(Ie.reduce(((e,t)=>e+t.tax),0))}</div>\n              </div>\n              <div class="summary-card">\n                <h3>${I}</h3>\n                <div class="value">${Gt(Ie.reduce(((e,t)=>e+t.discount),0))}</div>\n              </div>\n              <div class="summary-card">\n                <h3>${M}</h3>\n                <div class="value">${Ie.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).length}</div>\n              </div>\n              <div class="summary-card">\n                <h3>${T}</h3>\n                <div class="value">${Gt(Ie.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n              </div>\n            </div>\n          </div>\n        `:""}\n\n        <div class="footer">\n          <div class="footer-logo">\n            <strong>${P}</strong>\n          </div>\n          <p>${E}</p>\n          <p>© 2025 iDesign DZ +213 551 93 05 89 - ${A}</p>\n        </div>\n      </body>\n      </html>\n    `,O=window.open("","_blank","width=1200,height=800");O.document.write(R),O.document.close(),O.onload=function(){setTimeout((()=>{O.print()}),1e3)},Jt(`📊 ${n("salesReportOpened","تم فتح تقرير المبيعات الاحترافي للطباعة")}`,"success",3e3)},children:["📊 ",n("report","تقرير")]})})})]}),t.jsxs("div",{className:"sales-summary",children:[t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("totalSales","إجمالي المبيعات")}),t.jsx("div",{className:"summary-value",children:Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0))})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:n("invoiceCount","عدد الفواتير")}),t.jsx("div",{className:"summary-value",children:Ie.length})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("averageInvoice","متوسط الفاتورة")}),t.jsx("div",{className:"summary-value",children:Ie.length>0?Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0)/Ie.length):Gt(0)})]}),t.jsxs("div",{className:"summary-card purple",children:[t.jsx("h3",{children:n("creditInvoices","فواتير دين")}),t.jsx("div",{className:"summary-value",children:Ie.filter((e=>"دين"===e.paymentMethod||"Dette"===e.paymentMethod||"Credit"===e.paymentMethod)).length})]})]}),Ea.length>0&&t.jsxs("div",{className:"bulk-actions "+(yt.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:Ea.length>0&&yt.length===Ea.length,onChange:()=>Wt("sales",Ea),id:"select-all-sales"}),t.jsx("label",{htmlFor:"select-all-sales",className:"select-all-label",children:n("selectAll","تحديد الكل")}),yt.length>0&&t.jsxs("span",{className:"selected-count",children:["(",yt.length," ",n("selected","محدد"),")"]})]}),yt.length>0&&("مدير"===zt.role||"admin"===zt.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Kt("sales"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",yt.length,")"]})]}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",className:"search-input",placeholder:`🔍 ${n("searchSalesInvoices","البحث في فواتير المبيعات (رقم الفاتورة، اسم الزبون)...")}`,value:Ta,onChange:e=>Pa(e.target.value)}),Ta&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>Pa(""),title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==N?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:Ea.length>0&&yt.length===Ea.length,onChange:()=>Wt("sales",Ea),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("customer","الزبون")}),t.jsx("th",{children:n("paymentMethod","طريقة الدفع")}),t.jsx("th",{children:n("amount","المبلغ")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Ea.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"8",style:{textAlign:"center",padding:"20px"},children:Ta.trim()?`${n("noInvoicesMatchingFilter","لا توجد فواتير مطابقة للفلتر")}: "${Ta}"`:n("noInvoicesSaved","لا توجد فواتير محفوظة")})}):Ea.map((e=>t.jsxs("tr",{className:yt.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:yt.includes(e.id),onChange:()=>Yt("sales",e.id)})}),t.jsx("td",{children:e.invoiceNumber}),t.jsx("td",{children:e.date}),t.jsx("td",{children:Vt(e.customerName)}),t.jsx("td",{children:t.jsx("span",{className:"payment-method "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"),children:"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")})}),t.jsx("td",{children:Gt(e.finalTotal)}),t.jsx("td",{children:t.jsx("span",{className:"status "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"paid":"pending"),children:"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")})}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-info btn-xs",onClick:()=>mn(e),title:n("viewInvoiceDetails","عرض تفاصيل الفاتورة"),children:"👁️"}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>pn(e),title:n("normalPrint","طباعة عادية"),children:"🖨️"}),t.jsx("button",{className:"btn btn-success btn-xs",onClick:()=>hn(e),title:n("thermalPrint","طباعة حرارية"),children:"🧾"}),("مدير"===zt.role||"admin"===zt.role)&&t.jsxs(t.Fragment,{children:[t.jsx("button",{className:"btn btn-warning btn-xs",onClick:()=>(e=>{"مدير"===zt.role||"admin"===zt.role?(_a(""),Hn({...e}),Yn([...e.items]),Jn(!0)):Jt(`❌ ${n("notAllowedManagerOnlyEditInvoices","غير مسموح - المدير فقط يمكنه تعديل الفواتير")}`,"error")})(e),title:n("editInvoice","تعديل الفاتورة (المدير فقط)"),children:"✏️"}),t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>vn(e),title:n("returnProducts","إرجاع منتجات"),children:"↩️"}),t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{if(window.confirm(`${n("confirmDeleteInvoice","هل أنت متأكد من حذف الفاتورة رقم")} ${e.invoiceNumber}؟\n\n${n("allProductsWillBeRestored","سيتم إرجاع جميع المنتجات إلى المخزون.")}`)){const t=Mn.map((t=>{const n=e.items.find((e=>e.productId===t.id||String(e.productId)===String(t.id)));return n?{...t,stock:t.stock+n.quantity}:t})),a=Ie.filter((t=>t.id!==e.id));En(t),Me(a),localStorage.setItem("icaldz-invoices",JSON.stringify(a)),Jt(`🗑️ ${n("invoiceDeletedAndStockRestored","تم حذف الفاتورة")} ${e.invoiceNumber} ${n("andStockRestored","وإرجاع المنتجات للمخزون")}`,"success",3e3)}})(e),title:n("deleteInvoice","حذف الفاتورة (المدير فقط)"),children:"🗑️"})]}),("seller"===zt.role||"بائع"===zt.role)&&t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>vn(e),title:n("returnProducts","إرجاع منتجات"),children:"↩️"})]})})]},e.id)))})]})})]}),"inventory"===J&&t.jsxs("div",{className:"inventory-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["📊 ",n("inventoryManagement","إدارة المخزون")]})}),t.jsx("div",{className:"page-description-section",children:t.jsxs("div",{className:"header-actions",children:[t.jsxs("button",{className:"btn btn-success btn-compact",onClick:hs,children:["➕ ",n("newProduct","منتج جديد")]}),t.jsxs("div",{className:"dropdown-container",children:[t.jsxs("button",{className:"btn btn-primary btn-compact dropdown-toggle",onClick:()=>Se(!$e),children:["💾 ",n("dataManagement","البيانات")]}),$e&&t.jsxs("div",{className:"dropdown-menu",children:[t.jsxs("button",{className:"dropdown-item",onClick:()=>{if(Qt(Mn,"ar"===N?"تقرير_المخزون":"fr"===N?"Rapport_Inventaire":"Inventory_Report")){Jt("ar"===N?"📊 تم تصدير المخزون بتنسيق Excel بنجاح":"fr"===N?"📊 Inventaire exporté en Excel avec succès":"📊 Inventory exported to Excel successfully","success",3e3)}else{Jt("ar"===N?"❌ حدث خطأ أثناء تصدير الملف":"fr"===N?"❌ Erreur lors de l'export du fichier":"❌ Error occurred while exporting file","error",3e3)}Se(!1)},children:["📊 ",n("exportExcel","تصدير Excel")]}),t.jsxs("button",{className:"dropdown-item",onClick:()=>{const e={products:Mn,timestamp:(new Date).toISOString(),version:"1.0",totalProducts:Mn.length,totalValue:Mn.reduce(((e,t)=>e+t.stock*t.buyPrice),0)},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),n=document.createElement("a");n.href=URL.createObjectURL(t),n.download=`Inventory_Backup_${(new Date).toISOString().split("T")[0]}.json`,n.click(),Jt("💾 تم إنشاء نسخة احتياطية من البيانات","success",3e3),Se(!1)},children:["💾 ",n("jsonBackup","نسخة احتياطية JSON")]}),t.jsxs("button",{className:"dropdown-item",onClick:()=>{document.getElementById("import-file").click(),Se(!1)},children:["📥 ",n("importData","استيراد بيانات")]}),t.jsxs("button",{className:"dropdown-item danger",onClick:()=>{window.confirm(n("confirmDeleteAllData","هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!"))&&(localStorage.removeItem("icaldz-products"),Tn([]),Jt(`🗑️ ${n("allInventoryDataDeleted","تم حذف جميع بيانات المخزون")}`,"warning",3e3),Se(!1))},children:["🗑️ ",n("deleteAllData","حذف جميع البيانات")]})]})]}),t.jsxs("button",{className:"btn btn-info btn-compact",onClick:()=>{window.print(),Jt(`🖨️ ${n("reportSentToPrinter","تم إرسال التقرير للطباعة")}`,"success",2e3)},children:["🖨️ ",n("print","طباعة")]})]})})]}),t.jsx("input",{type:"file",id:"import-file",accept:".json,.xls,.xlsx,.csv",style:{display:"none"},onChange:async e=>{const t=e.target.files[0];if(t)try{if(t.name.endsWith(".json")){const e=new FileReader;e.onload=e=>{try{const t=e.target.result,n=JSON.parse(t);n.products&&Array.isArray(n.products)?(En(n.products),Jt(`✅ تم استيراد ${n.products.length} منتج بنجاح من JSON`,"success",3e3)):Jt("❌ تنسيق ملف JSON غير صحيح","error",3e3)}catch(t){Jt("❌ خطأ في قراءة ملف JSON","error",3e3)}},e.readAsText(t)}else if(t.name.endsWith(".xlsx")||t.name.endsWith(".xls"))try{const e=await(e=>new Promise(((t,n)=>{const a=new FileReader;a.onload=e=>{try{const n=new Uint8Array(e.target.result),a=i(n,{type:"array"}),r=a.SheetNames[0],o=a.Sheets[r],l=s.sheet_to_json(o,{header:1}).slice(1).filter((e=>e.length>0&&e[0]&&e[1])).map(((e,t)=>"string"==typeof e[0]&&(e[0].includes("الملخص")||e[0].includes("إجمالي")||e[0].includes("Résumé")||e[0].includes("Total")||e[0].includes("Summary")||e[0].includes("Produits"))?null:{id:e[0]||`IMP${String(t+1).padStart(3,"0")}`,name:e[1]||"منتج مستورد",barcode:"غير محدد"===e[2]?"":e[2]||"",category:e[3]||"عام",buyPrice:parseFloat(e[4])||0,sellPrice:parseFloat(e[5])||0,stock:parseInt(e[6])||0,minStock:parseInt(e[7])||5,price:parseFloat(e[5])||0,createdAt:(new Date).toISOString()})).filter((e=>null!==e));t(l)}catch(a){n(a)}},a.onerror=()=>n(new Error("فشل في قراءة الملف")),a.readAsArrayBuffer(e)})))(t);if(e.length>0){if(window.confirm(`تم العثور على ${e.length} منتج في الملف.\n\nاختر "موافق" لاستبدال جميع المنتجات الحالية\nأو "إلغاء" لإضافة المنتجات الجديدة فقط`))En(e),Jt(`✅ تم استبدال المخزون بـ ${e.length} منتج من Excel`,"success",3e3);else{const t=Mn.map((e=>e.id)),n=e.filter((e=>!t.includes(e.id))),a=[...Mn,...n];En(a),Jt(`✅ تم إضافة ${n.length} منتج جديد من Excel`,"success",3e3)}}else Jt("⚠️ لم يتم العثور على منتجات صالحة في الملف","warning",3e3)}catch(n){console.error("Excel import error:",n),Jt("❌ خطأ في قراءة ملف Excel: "+n.message,"error",3e3)}else Jt("⚠️ يدعم النظام ملفات JSON و Excel فقط (.json, .xlsx, .xls)","warning",3e3)}catch(n){Jt("❌ خطأ في معالجة الملف","error",3e3)}e.target.value=""}}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",placeholder:n("searchProducts","البحث في المنتجات (الاسم، الرمز، الباركود)..."),className:"search-input",value:ra,onChange:e=>ia(e.target.value)}),t.jsxs("div",{className:"category-filter-group",children:[t.jsxs("select",{className:"filter-select",value:je,onChange:e=>ye(e.target.value),children:[t.jsx("option",{value:"",children:n("allCategories","جميع الفئات")}),Fn.map((e=>t.jsx("option",{value:e,children:e},e)))]}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:kn,title:n("manageCategories","إدارة الفئات"),children:"⚙️"})]}),t.jsxs("select",{className:"filter-select",value:Ne,onChange:e=>we(e.target.value),children:[t.jsx("option",{value:"",children:n("allStatuses","جميع الحالات")}),t.jsx("option",{value:"عادي",children:n("normal","عادي")}),t.jsx("option",{value:"مرتفع",children:n("high","مرتفع")}),t.jsx("option",{value:"منخفض",children:n("low","منخفض")}),t.jsx("option",{value:"نفد",children:n("outOfStock","نفد")})]}),(ra||je||Ne)&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>{ia(""),ye(""),we("")},title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),vs.length>0&&t.jsxs("div",{className:"bulk-actions "+(Ct.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:vs.length>0&&Ct.length===vs.length,onChange:()=>Wt("products",vs),id:"select-all-products"}),t.jsx("label",{htmlFor:"select-all-products",className:"select-all-label",children:n("selectAll","تحديد الكل")}),Ct.length>0&&t.jsxs("span",{className:"selected-count",children:["(",Ct.length," ",n("selected","محدد"),")"]})]}),Ct.length>0&&("مدير"===zt.role||"admin"===zt.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Kt("products"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",Ct.length,")"]})]}),t.jsx("div",{className:"inventory-table-container",children:t.jsxs("table",{className:"inventory-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:vs.length>0&&Ct.length===vs.length,onChange:()=>Wt("products",vs),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("productCode","رمز المنتج")}),t.jsx("th",{children:n("productName","اسم المنتج")}),t.jsx("th",{children:n("barcode","الباركود")}),t.jsx("th",{children:n("category","الفئة")}),t.jsx("th",{children:n("buyPrice","سعر الشراء")}),t.jsx("th",{children:n("sellPrice","سعر البيع")}),t.jsx("th",{children:n("availableQuantity","الكمية المتوفرة")}),t.jsx("th",{children:n("minStock","الحد الأدنى")}),t.jsx("th",{children:n("totalValue","القيمة الإجمالية")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===vs.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"12",style:{textAlign:"center",padding:"40px",color:"#666"},children:ra||je||Ne?`🔍 ${n("noProductsFound","لا توجد منتجات تطابق معايير البحث")}`:`📦 ${n("noProductsInInventory","لا توجد منتجات في المخزون")}`})}):vs.map((e=>t.jsxs("tr",{className:`${e.stock<=e.minStock?"low-stock":""} ${Ct.includes(e.id)?"selected":""} clickable-row`,onClick:t=>{"checkbox"===t.target.type||t.target.closest(".action-buttons-group")||t.target.closest(".stock-input")||t.target.closest(".stock-display-vendor")||gs(e)},title:n("clickToEdit","انقر للتعديل"),children:[t.jsx("td",{className:"checkbox-column",onClick:e=>e.stopPropagation(),children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:Ct.includes(e.id),onChange:()=>Yt("products",e.id)})}),t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:t.jsx("span",{className:"barcode-display",children:e.barcode||n("notSpecified","غير محدد")})}),t.jsx("td",{children:e.category}),t.jsx("td",{children:Gt(e.buyPrice)}),t.jsx("td",{children:Gt(e.sellPrice)}),t.jsx("td",{onClick:e=>e.stopPropagation(),children:"مدير"===zt.role||"admin"===zt.role?t.jsx("input",{type:"number",value:e.stock,onChange:t=>xs(e.id,parseInt(t.target.value)||0),className:"stock-input",min:"0"}):t.jsxs("div",{className:"stock-display-vendor",children:[t.jsx("span",{className:"stock-value",children:e.stock}),t.jsx("button",{className:"btn btn-xs btn-warning",onClick:()=>(e=>{if("مدير"===zt.role||"admin"===zt.role){const t=prompt(`تعديل كمية ${e.name}\nالكمية الحالية: ${e.stock}`,e.stock);return void(null===t||isNaN(t)||xs(e.id,parseInt(t)))}const t=prompt(`طلب تعديل كمية ${e.name}\nالكمية الحالية: ${e.stock}\nأدخل الكمية المطلوبة:`,e.stock);if(null!==t&&!isNaN(t)){const n={id:"REQ-"+Date.now(),productId:e.id,productName:e.name,currentStock:e.stock,requestedStock:parseInt(t),requestedBy:zt.name,requestDate:(new Date).toISOString(),status:"pending",reason:prompt("سبب التعديل (اختياري):")||"تعديل كمية"},a=JSON.parse(localStorage.getItem("icaldz-stock-requests")||"[]");a.push(n),localStorage.setItem("icaldz-stock-requests",JSON.stringify(a)),Jt("📝 تم إرسال طلب تعديل الكمية للمدير","info",3e3)}})(e),title:n("requestStockUpdate","طلب تعديل الكمية (يحتاج موافقة المدير)"),children:"📝"})]})}),t.jsx("td",{children:e.minStock}),t.jsx("td",{children:Gt(e.stock*e.buyPrice)}),t.jsx("td",{children:t.jsx("span",{className:"stock-status "+(e.stock<=e.minStock?"low":e.stock>2*e.minStock?"high":"normal"),children:e.stock<=e.minStock?n("low","منخفض"):e.stock>2*e.minStock?n("high","مرتفع"):n("normal","عادي")})}),t.jsx("td",{onClick:e=>e.stopPropagation(),children:t.jsxs("div",{className:"action-buttons-group",style:{justifyContent:"flex-start",gap:"5px"},children:[t.jsx("button",{className:"btn btn-warning btn-xs",onClick:()=>gs(e),title:n("editProduct","تعديل المنتج"),children:"✏️"}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>generateProductBarcode(e),title:n("generateBarcode","إنشاء باركود"),children:"📊"}),("مدير"===zt.role||"admin"===zt.role)&&t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{const t=Mn.find((t=>t.id===e));if(window.confirm(`${n("confirmDeleteProduct","هل أنت متأكد من حذف المنتج")} "${t?.name}"؟`)){const a=Mn.filter((t=>t.id!==e));En(a),b.play("deleteProduct",{showNotification:!1}),Jt(`🗑️ ${n("productDeletedSuccessfully","تم حذف المنتج")} "${t?.name}" ${n("successfully","بنجاح")}`,"success",2e3)}})(e.id),title:n("deleteProduct","حذف (المدير فقط)"),children:"🗑️"})]})})]},e.id)))})]})}),t.jsxs("div",{className:"inventory-summary",children:[t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:ra||je||Ne?n("displayedProducts","المنتجات المعروضة"):n("totalProducts","إجمالي المنتجات")}),t.jsx("div",{className:"summary-value",children:vs.length}),(ra||je||Ne)&&t.jsxs("small",{style:{color:"#666",fontSize:"12px"},children:[n("outOfTotal","من أصل")," ",Mn.length," ",n("product","منتج")]})]}),t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("totalValue","القيمة الإجمالية")}),t.jsx("div",{className:"summary-value",children:Gt(vs.reduce(((e,t)=>e+t.stock*t.buyPrice),0))})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("lowStockProducts","منتجات منخفضة المخزون")}),t.jsx("div",{className:"summary-value",children:vs.filter((e=>e.stock<=e.minStock)).length})]}),t.jsxs("div",{className:"summary-card red",children:[t.jsx("h3",{children:n("outOfStockProducts","منتجات نفدت")}),t.jsx("div",{className:"summary-value",children:vs.filter((e=>0===e.stock)).length})]})]})]}),"reports"===J&&("مدير"===zt.role||"admin"===zt.role)&&t.jsxs("div",{className:"reports-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["📊 ",n("reportsAndStatistics","التقارير والإحصائيات")]})}),t.jsx("div",{className:"page-description-section",children:t.jsx("p",{children:n("comprehensiveReports","تقارير شاملة لجميع العمليات المالية والتجارية - المدير فقط")})})]}),t.jsxs("div",{className:"reports-categories",children:[t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["💰 ",n("financialReports","التقارير المالية")]}),t.jsx("p",{children:n("financialReportsDesc","تقارير المبيعات والمشتريات والأرباح")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("salesReport","تقرير المبيعات")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #27ae60; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #3498db; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📈 ${n("salesReport","تقرير المبيعات التفصيلي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("salesManagement","إدارة المبيعات")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalSales","إجمالي المبيعات")}</h3>\n            <div class="value">${Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("invoiceCount","عدد الفواتير")}</h3>\n            <div class="value">${Ie.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averageInvoice","متوسط قيمة الفاتورة")}</h3>\n            <div class="value">${Gt(Ie.length>0?Ie.reduce(((e,t)=>e+t.finalTotal),0)/Ie.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("cashSales","المبيعات النقدية")}</h3>\n            <div class="value">${Gt(Ie.filter((e=>"نقداً"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("invoiceNumber","رقم الفاتورة")}</th>\n                <th>${n("date","التاريخ")}</th>\n                <th>${n("customerName","العميل")}</th>\n                <th>${n("paymentMethod","طريقة الدفع")}</th>\n                <th>${n("subtotal","المبلغ الإجمالي")}</th>\n                <th>${n("tax","الضريبة")}</th>\n                <th>${n("finalAmount","المبلغ النهائي")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${Ie.map((e=>`\n                <tr>\n                  <td>${e.invoiceNumber}</td>\n                  <td>${e.date}</td>\n                  <td>${e.customerName||n("walkInCustomer","زبون عابر")}</td>\n                  <td>${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}</td>\n                  <td>${Gt(e.total)}</td>\n                  <td>${Gt(e.tax)}</td>\n                  <td>${Gt(e.finalTotal)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Jt(n("salesReportOpened","📈 تم فتح تقرير المبيعات للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"📈"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("salesReport","تقرير المبيعات")}),t.jsx("p",{children:n("salesReportDesc","إجمالي المبيعات والفواتير")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0))}),t.jsxs("small",{children:[Ie.length," ",n("invoice","فاتورة")]})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("purchaseReport","تقرير المشتريات")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #e74c3c; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #e74c3c; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📉 ${n("purchaseReport","تقرير المشتريات التفصيلي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("purchaseManagement","إدارة المشتريات")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalPurchases","إجمالي المشتريات")}</h3>\n            <div class="value">${Gt(et.reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("invoiceCount","عدد فواتير الشراء")}</h3>\n            <div class="value">${et.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averageInvoice","متوسط قيمة الفاتورة")}</h3>\n            <div class="value">${Gt(et.length>0?et.reduce(((e,t)=>e+t.finalTotal),0)/et.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("cashPurchases","المشتريات النقدية")}</h3>\n            <div class="value">${Gt(et.filter((e=>"نقداً"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("invoiceNumber","رقم الفاتورة")}</th>\n                <th>${n("date","التاريخ")}</th>\n                <th>${n("supplier","المورد")}</th>\n                <th>${n("paymentMethod","طريقة الدفع")}</th>\n                <th>${n("subtotal","المبلغ الإجمالي")}</th>\n                <th>${n("tax","الضريبة")}</th>\n                <th>${n("finalAmount","المبلغ النهائي")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${et.map((e=>`\n                <tr>\n                  <td>${e.invoiceNumber}</td>\n                  <td>${e.date}</td>\n                  <td>${e.supplierName||n("unknownSupplier","مورد غير محدد")}</td>\n                  <td>${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}</td>\n                  <td>${Gt(e.total)}</td>\n                  <td>${Gt(e.tax)}</td>\n                  <td>${Gt(e.finalTotal)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Jt(n("purchaseReportOpened","📉 تم فتح تقرير المشتريات للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"📉"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("purchaseReport","تقرير المشتريات")}),t.jsx("p",{children:n("purchaseReportDesc","إجمالي المشتريات والموردين")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Gt(et.reduce(((e,t)=>e+t.finalTotal),0))}),t.jsxs("small",{children:[et.length," ",n("invoice","فاتورة")]})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=Ie.reduce(((e,t)=>e+(parseFloat(t.finalTotal)||0)),0),s=d(Ie,Mn).totalCost,r=at.reduce(((e,t)=>e+(parseFloat(t.amount)||0)),0),i=a-s-r,o=a>0?(i/a*100).toFixed(2):0,l=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("profitReport","تقرير الأرباح")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .profit-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .profit-card { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .profit-card.positive { border-left: 5px solid #27ae60; }\n          .profit-card.negative { border-left: 5px solid #e74c3c; }\n          .profit-card h3 { margin: 0 0 15px 0; color: #34495e; }\n          .profit-card .value { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n          .profit-card.positive .value { color: #27ae60; }\n          .profit-card.negative .value { color: #e74c3c; }\n          .profit-card .description { color: #7f8c8d; font-size: 14px; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .profit-card { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💎 ${n("profitLossReport","تقرير الأرباح والخسائر")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("financialAnalysis","التحليل المالي")}</p>\n        </div>\n\n        <div class="profit-summary">\n          <div class="profit-card positive">\n            <h3>${n("totalSales","إجمالي المبيعات")}</h3>\n            <div class="value">${Gt(a)}</div>\n            <div class="description">${n("totalRevenueFromSales","إجمالي الإيرادات من المبيعات")}</div>\n          </div>\n\n          <div class="profit-card negative">\n            <h3>${n("costOfGoodsSold","تكلفة البضاعة المباعة")}</h3>\n            <div class="value">${Gt(s)}</div>\n            <div class="description">${n("costOfProductsSold","تكلفة المنتجات المباعة")}</div>\n          </div>\n\n          <div class="profit-card negative">\n            <h3>${n("operatingExpenses","المصاريف التشغيلية")}</h3>\n            <div class="value">${Gt(r)}</div>\n            <div class="description">${n("salariesRentOtherExpenses","الرواتب، الإيجار، والمصاريف الأخرى")}</div>\n          </div>\n\n          <div class="profit-card ${i>=0?"positive":"negative"}">\n            <h3>${n("netProfitLoss","صافي الربح/الخسارة")}</h3>\n            <div class="value">${Gt(i)}</div>\n            <div class="description">${i>=0?n("netProfit","ربح"):n("netLoss","خسارة")} ${n("net","صافية")}</div>\n          </div>\n\n          <div class="profit-card">\n            <h3>${n("profitMargin","هامش الربح")}</h3>\n            <div class="value" style="color: #3498db;">${o}%</div>\n            <div class="description">${n("profitPercentageFromSales","نسبة الربح من المبيعات")}</div>\n          </div>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,c=window.open("","_blank","width=1200,height=800");c.document.write(l),c.document.close(),c.onload=()=>setTimeout((()=>c.print()),1e3),Jt(n("profitReportOpened","💎 تم فتح تقرير الأرباح للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💎"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("profitReport","تقرير الأرباح")}),t.jsx("p",{children:n("profitReportDesc","تحليل الأرباح والخسائر")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0)-et.reduce(((e,t)=>e+t.finalTotal),0))}),t.jsx("small",{children:n("netProfit","صافي الربح")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e=Ie.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),t=Ie.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),a=et.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),s=et.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),r=e-a,i="ar"===N,o="ar"===N?"ar":"fr"===N?"fr":"en",l=`\n      <!DOCTYPE html>\n      <html dir="${i?"rtl":"ltr"}" lang="${o}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("cashFlowReport","تقرير التدفق النقدي")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${i?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .cash-flow-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .cash-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .cash-card.inflow { border-left: 5px solid #27ae60; }\n          .cash-card.outflow { border-left: 5px solid #e74c3c; }\n          .cash-card.net { border-left: 5px solid #3498db; }\n          .cash-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .cash-card .value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }\n          .cash-card.inflow .value { color: #27ae60; }\n          .cash-card.outflow .value { color: #e74c3c; }\n          .cash-card.net .value { color: #3498db; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .cash-card { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💸 ${n("cashFlowReport","تقرير التدفق النقدي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===o?"ar-DZ":"fr"===o?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("cashManagement","إدارة النقدية")}</p>\n        </div>\n\n        <div class="cash-flow-grid">\n          <div class="cash-card inflow">\n            <h3>${n("cashSales","المبيعات النقدية")}</h3>\n            <div class="value">${Gt(e)}</div>\n            <small>${n("cashInflow","نقد داخل")}</small>\n          </div>\n\n          <div class="cash-card outflow">\n            <h3>${n("cashPurchases","المشتريات النقدية")}</h3>\n            <div class="value">${Gt(a)}</div>\n            <small>${n("cashOutflow","نقد خارج")}</small>\n          </div>\n\n          <div class="cash-card net">\n            <h3>${n("netCashFlow","صافي التدفق النقدي")}</h3>\n            <div class="value">${Gt(r)}</div>\n            <small>${r>=0?n("cashSurplus","فائض نقدي"):n("cashDeficit","عجز نقدي")}</small>\n          </div>\n\n          <div class="cash-card">\n            <h3>${n("creditSales","المبيعات الآجلة")}</h3>\n            <div class="value" style="color: #f39c12;">${Gt(t)}</div>\n            <small>${n("receivables","ذمم مدينة")}</small>\n          </div>\n\n          <div class="cash-card">\n            <h3>${n("creditPurchases","المشتريات الآجلة")}</h3>\n            <div class="value" style="color: #9b59b6;">${Gt(s)}</div>\n            <small>${n("payables","ذمم دائنة")}</small>\n          </div>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,c=window.open("","_blank","width=1200,height=800");c.document.write(l),c.document.close(),c.onload=()=>setTimeout((()=>c.print()),1e3),Jt(n("cashFlowReportOpened","💸 تم فتح تقرير التدفق النقدي للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💸"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("cashFlowReport","تقرير التدفق النقدي")}),t.jsx("p",{children:n("cashFlowReportDesc","حركة النقد الداخل والخارج")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Gt(Ie.filter((e=>"نقداً"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}),t.jsx("small",{children:n("inCash","نقداً")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]}),t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["📦 ",n("inventoryReports","تقارير المخزون")]}),t.jsx("p",{children:n("inventoryReportsDesc","حالة المخزون والمنتجات")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>{Qt(Mn,"ar"===N?"تقرير_المخزون_العام":"fr"===N?"Rapport_Inventaire_General":"General_Inventory_Report")?Jt("ar"===N?"📊 تم تصدير تقرير المخزون العام بتنسيق Excel":"fr"===N?"📊 Rapport d'inventaire général exporté en Excel avec succès":"📊 General inventory report exported to Excel successfully","success",3e3):Jt("ar"===N?"❌ حدث خطأ أثناء تصدير التقرير":"fr"===N?"❌ Erreur lors de l'export du rapport":"❌ Error occurred while exporting report","error",3e3)},children:[t.jsx("div",{className:"report-icon",children:"📊"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("generalInventoryReport","تقرير المخزون العام")}),t.jsx("p",{children:n("generalInventoryReportDesc","جميع المنتجات وحالة المخزون")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Mn.length}),t.jsx("small",{children:n("product","منتج")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e=Mn.filter((e=>e.stock<=e.minStock));Qt(e,"ar"===N?"تقرير_المخزون_المنخفض":"fr"===N?"Rapport_Stock_Faible":"Low_Stock_Report")?Jt("ar"===N?"⚠️ تم تصدير تقرير المخزون المنخفض بتنسيق Excel":"fr"===N?"⚠️ Rapport de stock faible exporté en Excel avec succès":"⚠️ Low stock report exported to Excel successfully","success",3e3):Jt("ar"===N?"❌ حدث خطأ أثناء تصدير التقرير":"fr"===N?"❌ Erreur lors de l'export du rapport":"❌ Error occurred while exporting report","error",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"⚠️"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("lowStockReport","تقرير المخزون المنخفض")}),t.jsx("p",{children:n("lowStockReportDesc","المنتجات التي تحتاج إعادة تموين")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Mn.filter((e=>e.stock<=e.minStock)).length}),t.jsx("small",{children:n("product","منتج")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("inventoryValueReport","تقرير قيمة المخزون")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .value-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .value-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .value-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .value-card .value { font-size: 24px; font-weight: bold; color: #27ae60; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #27ae60; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .value-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💰 ${n("inventoryValueReport","تقرير قيمة المخزون")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("inventoryValuation","تقييم المخزون")}</p>\n        </div>\n\n        <div class="value-summary">\n          <div class="value-card">\n            <h3>${n("totalInventoryValue","إجمالي قيمة المخزون")}</h3>\n            <div class="value">${Gt(Mn.reduce(((e,t)=>e+t.stock*t.buyPrice),0))}</div>\n          </div>\n          <div class="value-card">\n            <h3>${n("productCount","عدد المنتجات")}</h3>\n            <div class="value">${Mn.length}</div>\n          </div>\n          <div class="value-card">\n            <h3>${n("totalQuantities","إجمالي الكميات")}</h3>\n            <div class="value">${Mn.reduce(((e,t)=>e+t.stock),0)}</div>\n          </div>\n          <div class="value-card">\n            <h3>${n("averageProductValue","متوسط قيمة المنتج")}</h3>\n            <div class="value">${Gt(Mn.length>0?Mn.reduce(((e,t)=>e+t.stock*t.buyPrice),0)/Mn.length:0)}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("productName","اسم المنتج")}</th>\n                <th>${n("category","الفئة")}</th>\n                <th>${n("quantity","الكمية")}</th>\n                <th>${n("buyPrice","سعر الشراء")}</th>\n                <th>${n("totalValue","القيمة الإجمالية")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${Mn.map((e=>`\n                <tr>\n                  <td>${e.name}</td>\n                  <td>${e.category}</td>\n                  <td>${e.stock}</td>\n                  <td>${Gt(e.buyPrice)}</td>\n                  <td>${Gt(e.stock*e.buyPrice)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ</p>\n          <p>© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Jt(n("inventoryValueReportOpened","💰 تم فتح تقرير قيمة المخزون للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💰"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("inventoryValueReport","تقرير قيمة المخزون")}),t.jsx("p",{children:n("inventoryValueReportDesc","القيمة الإجمالية للمخزون")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Gt(Mn.reduce(((e,t)=>e+t.stock*t.buyPrice),0))}),t.jsx("small",{children:n("totalValue","قيمة إجمالية")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=[...new Set(Mn.map((e=>e.category)))].map((e=>{const t=Mn.filter((t=>t.category===e));return{category:e,count:t.length,totalStock:t.reduce(((e,t)=>e+t.stock),0),totalValue:t.reduce(((e,t)=>e+t.stock*t.buyPrice),0)}})),s=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("categoriesReport","تقرير الفئات")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #9b59b6; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>🏷️ ${n("categoriesReport","تقرير الفئات")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("categoryAnalysis","تحليل الفئات")}</p>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("category","الفئة")}</th>\n                <th>${n("productCount","عدد المنتجات")}</th>\n                <th>${n("totalQuantities","إجمالي الكميات")}</th>\n                <th>${n("totalValue","القيمة الإجمالية")}</th>\n                <th>${n("inventoryPercentage","النسبة من المخزون")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${a.map((e=>`\n                <tr>\n                  <td>${e.category}</td>\n                  <td>${e.count}</td>\n                  <td>${e.totalStock}</td>\n                  <td>${Gt(e.totalValue)}</td>\n                  <td>${(e.totalValue/Mn.reduce(((e,t)=>e+t.stock*t.buyPrice),0)*100).toFixed(1)}%</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,r=window.open("","_blank","width=1200,height=800");r.document.write(s),r.document.close(),r.onload=()=>setTimeout((()=>r.print()),1e3),Jt(n("categoriesReportOpened","🏷️ تم فتح تقرير الفئات للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"🏷️"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("categoriesReport","تقرير الفئات")}),t.jsx("p",{children:n("categoriesReportDesc","تحليل المنتجات حسب الفئات")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:[...new Set(Mn.map((e=>e.category)))].length}),t.jsx("small",{children:n("category","فئة")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]}),t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["👥 ",n("customerReports","تقارير العملاء")]}),t.jsx("p",{children:n("customerReportsDesc","تحليل العملاء والمبيعات")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("customersReport","تقرير العملاء")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #8e44ad; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #8e44ad; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .status-active { color: #27ae60; font-weight: bold; }\n          .status-inactive { color: #e74c3c; font-weight: bold; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>👥 ${n("customersReport","تقرير العملاء التفصيلي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("customerManagement","إدارة العملاء")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalCustomers","إجمالي العملاء")}</h3>\n            <div class="value">${fa.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("activeCustomers","العملاء النشطون")}</h3>\n            <div class="value">${fa.filter((e=>"نشط"===e.status)).length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("newCustomersThisMonth","العملاء الجدد هذا الشهر")}</h3>\n            <div class="value">${fa.filter((e=>{const t=new Date(e.createdAt),n=new Date;return t.getMonth()===n.getMonth()&&t.getFullYear()===n.getFullYear()})).length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averagePurchasesPerCustomer","متوسط المشتريات للعميل")}</h3>\n            <div class="value">${fa.length>0?Math.round(Ie.length/fa.length):0}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("customerNumber","رقم العميل")}</th>\n                <th>${n("customerName","اسم العميل")}</th>\n                <th>${n("phoneNumber","رقم الهاتف")}</th>\n                <th>${n("address","العنوان")}</th>\n                <th>${n("registrationDate","تاريخ التسجيل")}</th>\n                <th>${n("purchaseCount","عدد المشتريات")}</th>\n                <th>${n("totalPurchases","إجمالي المشتريات")}</th>\n                <th>${n("status","الحالة")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${fa.map((e=>{const a=Ie.filter((t=>t.customerName===e.name)),s=a.reduce(((e,t)=>e+t.finalTotal),0);return`\n                  <tr>\n                    <td>${e.id}</td>\n                    <td>${e.name}</td>\n                    <td>${e.phone||n("notSpecified","غير محدد")}</td>\n                    <td>${e.address||n("notSpecified","غير محدد")}</td>\n                    <td>${new Date(e.createdAt).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${a.length}</td>\n                    <td>${Gt(s)}</td>\n                    <td class="${"نشط"===e.status?"status-active":"status-inactive"}">${"نشط"===e.status?n("active","نشط"):n("inactive","غير نشط")}</td>\n                  </tr>\n                `})).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Jt(n("customersReportOpened","👤 تم فتح تقرير العملاء للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"👤"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("customersReport","تقرير العملاء")}),t.jsx("p",{children:n("customersReportDesc","قائمة العملاء وبياناتهم")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:fa.length}),t.jsx("small",{children:n("customer","عميل")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=Ie.filter((e=>"دين"===e.paymentMethod)),s={};a.forEach((e=>{s[e.customerName]||(s[e.customerName]={customerName:e.customerName,totalDebt:0,invoiceCount:0,invoices:[]}),s[e.customerName].totalDebt+=e.finalTotal,s[e.customerName].invoiceCount+=1,s[e.customerName].invoices.push(e)}));const r=Object.values(s),i=a.reduce(((e,t)=>e+t.finalTotal),0),o=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("debtorsReport","تقرير المدينين")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .alert-box { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; margin-bottom: 30px; text-align: center; }\n          .alert-box h2 { color: #856404; margin: 0 0 10px 0; }\n          .alert-box .total-debt { font-size: 32px; font-weight: bold; color: #d63031; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #e74c3c; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #e74c3c; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .high-debt { background: #ffebee !important; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💳 ${n("debtorsAndReceivables","تقرير المدينين والمستحقات")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("debtManagement","إدارة الديون")}</p>\n        </div>\n\n        <div class="alert-box">\n          <h2>⚠️ ${n("totalOutstandingAmount","إجمالي المبالغ المستحقة")}</h2>\n          <div class="total-debt">${Gt(i)}</div>\n          <p>${n("followUpRequired","يجب متابعة تحصيل هذه المبالغ")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("debtorCustomers","عدد العملاء المدينين")}</h3>\n            <div class="value">${r.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("outstandingInvoices","عدد الفواتير المستحقة")}</h3>\n            <div class="value">${a.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averageDebtPerCustomer","متوسط الدين للعميل")}</h3>\n            <div class="value">${Gt(r.length>0?i/r.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("highestOutstandingAmount","أكبر مبلغ مستحق")}</h3>\n            <div class="value">${Gt(r.length>0?Math.max(...r.map((e=>e.totalDebt))):0)}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("customerName","اسم العميل")}</th>\n                <th>${n("invoiceCount","عدد الفواتير")}</th>\n                <th>${n("totalOutstanding","إجمالي المبلغ المستحق")}</th>\n                <th>${n("oldestInvoice","أقدم فاتورة")}</th>\n                <th>${n("newestInvoice","أحدث فاتورة")}</th>\n                <th>${n("priorityLevel","مستوى الأولوية")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${r.sort(((e,t)=>t.totalDebt-e.totalDebt)).map((e=>{const a=e.invoices.sort(((e,t)=>new Date(e.date)-new Date(t.date)))[0],s=e.invoices.sort(((e,t)=>new Date(t.date)-new Date(e.date)))[0],r=e.totalDebt>5e4?n("high","عالية"):e.totalDebt>2e4?n("medium","متوسطة"):n("low","منخفضة");return`\n                  <tr class="${e.totalDebt>5e4?"high-debt":""}">\n                    <td>${e.customerName}</td>\n                    <td>${e.invoiceCount}</td>\n                    <td>${Gt(e.totalDebt)}</td>\n                    <td>${new Date(a.date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${new Date(s.date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${r}</td>\n                  </tr>\n                `})).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,l=window.open("","_blank","width=1200,height=800");l.document.write(o),l.document.close(),l.onload=()=>setTimeout((()=>l.print()),1e3),Jt(n("debtorsReportOpened","💳 تم فتح تقرير المدينين للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💳"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("debtorsReport","تقرير المدينين")}),t.jsx("p",{children:n("debtorsReportDesc","العملاء المدينين والمستحقات")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Gt(Ie.filter((e=>"دين"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}),t.jsx("small",{children:n("totalDebts","إجمالي الديون")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a={};Ie.forEach((e=>{a[e.customerName]||(a[e.customerName]={name:e.customerName,totalPurchases:0,invoiceCount:0,averageInvoice:0,lastPurchase:e.date,firstPurchase:e.date}),a[e.customerName].totalPurchases+=e.finalTotal,a[e.customerName].invoiceCount+=1,new Date(e.date)>new Date(a[e.customerName].lastPurchase)&&(a[e.customerName].lastPurchase=e.date),new Date(e.date)<new Date(a[e.customerName].firstPurchase)&&(a[e.customerName].firstPurchase=e.date)})),Object.values(a).forEach((e=>{e.averageInvoice=e.totalPurchases/e.invoiceCount}));const s=Object.values(a).sort(((e,t)=>t.totalPurchases-e.totalPurchases)).slice(0,10),r=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("topCustomers","تقرير أفضل العملاء")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .top-customer-highlight { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; text-align: center; }\n          .top-customer-highlight h2 { margin: 0 0 15px 0; font-size: 24px; }\n          .top-customer-highlight .customer-name { font-size: 32px; font-weight: bold; margin-bottom: 10px; }\n          .top-customer-highlight .customer-value { font-size: 28px; margin-bottom: 5px; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #f39c12; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #f39c12; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .rank-1 { background: #fff9c4 !important; font-weight: bold; }\n          .rank-2 { background: #f0f0f0 !important; }\n          .rank-3 { background: #ffeaa7 !important; }\n          .rank { font-weight: bold; font-size: 18px; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>🌟 ${n("topCustomers","تقرير أفضل العملاء")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("topCustomersAnalysis","تحليل العملاء المميزين")}</p>\n        </div>\n\n        ${s.length>0?`\n          <div class="top-customer-highlight">\n            <h2>🏆 ${n("topCustomer","العميل الأول")}</h2>\n            <div class="customer-name">${s[0].name}</div>\n            <div class="customer-value">${Gt(s[0].totalPurchases)}</div>\n            <p>${s[0].invoiceCount} ${n("invoice","فاتورة")} | ${n("averageInvoice","متوسط الفاتورة")}: ${Gt(s[0].averageInvoice)}</p>\n          </div>\n        `:""}\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalSalesTopTen","إجمالي مبيعات أفضل 10 عملاء")}</h3>\n            <div class="value">${Gt(s.reduce(((e,t)=>e+t.totalPurchases),0))}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averagePurchasesTopCustomer","متوسط المشتريات للعميل المميز")}</h3>\n            <div class="value">${Gt(s.length>0?s.reduce(((e,t)=>e+t.totalPurchases),0)/s.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("totalInvoicesCount","إجمالي الفواتير")}</h3>\n            <div class="value">${s.reduce(((e,t)=>e+t.invoiceCount),0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("percentageOfTotalSales","نسبة من إجمالي المبيعات")}</h3>\n            <div class="value">${Ie.length>0?(s.reduce(((e,t)=>e+t.totalPurchases),0)/Ie.reduce(((e,t)=>e+t.finalTotal),0)*100).toFixed(1):0}%</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("ranking","الترتيب")}</th>\n                <th>${n("customerName","اسم العميل")}</th>\n                <th>${n("totalPurchases","إجمالي المشتريات")}</th>\n                <th>${n("invoiceCount","عدد الفواتير")}</th>\n                <th>${n("averageInvoice","متوسط الفاتورة")}</th>\n                <th>${n("firstPurchase","أول شراء")}</th>\n                <th>${n("lastPurchase","آخر شراء")}</th>\n                <th>${n("membershipPeriod","فترة العضوية")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${s.map(((e,a)=>{const s=Math.floor((new Date(e.lastPurchase)-new Date(e.firstPurchase))/864e5);return`\n                  <tr class="${0===a?"rank-1":1===a?"rank-2":2===a?"rank-3":""}">\n                    <td class="rank">${0===a?"🥇":1===a?"🥈":2===a?"🥉":""} ${a+1}</td>\n                    <td>${e.name}</td>\n                    <td>${Gt(e.totalPurchases)}</td>\n                    <td>${e.invoiceCount}</td>\n                    <td>${Gt(e.averageInvoice)}</td>\n                    <td>${new Date(e.firstPurchase).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${new Date(e.lastPurchase).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${s} ${n("days","يوم")}</td>\n                  </tr>\n                `})).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,i=window.open("","_blank","width=1200,height=800");i.document.write(r),i.document.close(),i.onload=()=>setTimeout((()=>i.print()),1e3),Jt(n("topCustomersReportOpened","🌟 تم فتح تقرير أفضل العملاء للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"🌟"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("topCustomersReport","أفضل العملاء")}),t.jsx("p",{children:n("topCustomersReportDesc","العملاء الأكثر شراءً")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:"TOP 10"}),t.jsx("small",{children:n("customer","عميل")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e=new Date,t=e.getMonth(),a=e.getFullYear(),s=fa.length,r=fa.filter((e=>"نشط"===e.status)).length,i=fa.filter((e=>{const n=new Date(e.createdAt);return n.getMonth()===t&&n.getFullYear()===a})).length,o=[...new Set(Ie.map((e=>e.customerName)))].length,l=[...new Set(Ie.filter((e=>"دين"===e.paymentMethod)).map((e=>e.customerName)))].length,c={};Ie.forEach((e=>{c[e.customerName]||(c[e.customerName]=0),c[e.customerName]++}));const d=Object.values(c).filter((e=>1===e)).length,m=Object.values(c).filter((e=>e>=2&&e<=5)).length,h=Object.values(c).filter((e=>e>=6&&e<=15)).length,u=Object.values(c).filter((e=>e>15)).length,p={};fa.forEach((e=>{const t=new Date(e.createdAt),n=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}`;p[n]||(p[n]=0),p[n]++}));const x="ar"===N,g="ar"===N?"ar":"fr"===N?"fr":"en",v=`\n      <!DOCTYPE html>\n      <html dir="${x?"rtl":"ltr"}" lang="${g}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("advancedCustomerAnalysis","تحليل العملاء المتقدم")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${x?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .analysis-section { background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .analysis-section h2 { color: #2c3e50; margin: 0 0 20px 0; font-size: 22px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 25px; }\n          .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #3498db; }\n          .stat-card h4 { margin: 0 0 8px 0; color: #34495e; font-size: 14px; }\n          .stat-card .value { font-size: 20px; font-weight: bold; color: #2c3e50; }\n          .frequency-chart { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }\n          .frequency-item { background: #ecf0f1; padding: 15px; border-radius: 8px; text-align: center; }\n          .frequency-item h4 { margin: 0 0 8px 0; color: #34495e; }\n          .frequency-item .count { font-size: 24px; font-weight: bold; color: #e67e22; }\n          .frequency-item .percentage { font-size: 12px; color: #7f8c8d; }\n          .insights-list { list-style: none; padding: 0; }\n          .insights-list li { background: #e8f6f3; padding: 12px; margin-bottom: 8px; border-radius: 6px; border-left: 4px solid #27ae60; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .analysis-section { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📊 ${n("advancedCustomerAnalysis","تحليل العملاء المتقدم")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===g?"ar-DZ":"fr"===g?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("businessIntelligence","ذكاء الأعمال")}</p>\n        </div>\n\n        <div class="analysis-section">\n          <h2>📈 ${n("generalCustomerStats","إحصائيات العملاء العامة")}</h2>\n          <div class="stats-grid">\n            <div class="stat-card">\n              <h4>${n("totalCustomers","إجمالي العملاء")}</h4>\n              <div class="value">${s}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("activeCustomers","العملاء النشطون")}</h4>\n              <div class="value">${r}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("newCustomersThisMonth","عملاء جدد هذا الشهر")}</h4>\n              <div class="value">${i}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("customersWithPurchases","عملاء لديهم مشتريات")}</h4>\n              <div class="value">${o}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("debtorCustomersCount","عملاء مدينون")}</h4>\n              <div class="value">${l}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("conversionRate","معدل التحويل")}</h4>\n              <div class="value">${s>0?(o/s*100).toFixed(1):0}%</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="analysis-section">\n          <h2>🔄 ${n("purchaseFrequencyAnalysis","تحليل تكرار الشراء")}</h2>\n          <div class="frequency-chart">\n            <div class="frequency-item">\n              <h4>${n("oneTimeBuyers","مشترون لمرة واحدة")}</h4>\n              <div class="count">${d}</div>\n              <div class="percentage">${o>0?(d/o*100).toFixed(1):0}%</div>\n            </div>\n            <div class="frequency-item">\n              <h4>${n("occasionalBuyers","مشترون أحياناً")}</h4>\n              <div class="count">${m}</div>\n              <div class="percentage">${o>0?(m/o*100).toFixed(1):0}%</div>\n            </div>\n            <div class="frequency-item">\n              <h4>${n("regularBuyers","مشترون منتظمون")}</h4>\n              <div class="count">${h}</div>\n              <div class="percentage">${o>0?(h/o*100).toFixed(1):0}%</div>\n            </div>\n            <div class="frequency-item">\n              <h4>${n("frequentBuyers","مشترون دائمون")}</h4>\n              <div class="count">${u}</div>\n              <div class="percentage">${o>0?(u/o*100).toFixed(1):0}%</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="analysis-section">\n          <h2>💡 ${n("insightsAndRecommendations","رؤى وتوصيات")}</h2>\n          <ul class="insights-list">\n            <li><strong>${n("retentionRate","معدل الاحتفاظ")}:</strong> ${d>0?`${(100*(1-d/o)).toFixed(1)}%`:"0%"} ${n("customersReturnToBuy","من العملاء يعودون للشراء مرة أخرى")}</li>\n            <li><strong>${n("growthOpportunity","فرصة النمو")}:</strong> ${d} ${n("oneTimePurchaseOpportunity","عميل اشترى مرة واحدة فقط - فرصة لحملات إعادة الاستهداف")}</li>\n            <li><strong>${n("loyalCustomers","العملاء المخلصون")}:</strong> ${u} ${n("regularPurchasers","عميل يشترون بانتظام - يجب الاهتمام بهم وتقديم عروض خاصة")}</li>\n            <li><strong>${n("customerGrowth","نمو العملاء")}:</strong> ${i} ${n("newCustomersThisMonthInsight","عميل جديد هذا الشهر")} ${i>0?n("positiveGrowth","- نمو إيجابي"):n("needsMarketingImprovement","- يحتاج تحسين التسويق")}</li>\n            <li><strong>${n("debtManagement","إدارة الديون")}:</strong> ${l} ${n("debtFollowUpNeeded","عميل لديه ديون - يحتاج متابعة للتحصيل")}</li>\n          </ul>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,b=window.open("","_blank","width=1200,height=800");b.document.write(v),b.document.close(),b.onload=()=>setTimeout((()=>b.print()),1e3),Jt(n("customerAnalysisReportOpened","📊 تم فتح تحليل العملاء المتقدم للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"📊"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("customerAnalysisReport","تحليل العملاء")}),t.jsx("p",{children:n("customerAnalysisReportDesc","إحصائيات مفصلة للعملاء")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:fa.filter((e=>"نشط"===e.status)).length}),t.jsx("small",{children:n("activeCustomer","عميل نشط")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]}),t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["📈 ",n("performanceReports","تقارير الأداء")]}),t.jsx("p",{children:n("performanceReportsDesc","مؤشرات الأداء والإحصائيات")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>(console.log("🚀 Starting NEW CLEAN daily report generation..."),void m(Ie,Mn,at,Gt,Jt,N)),children:[t.jsx("div",{className:"report-icon",children:"📅"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("dailyReport","التقرير اليومي")}),t.jsx("p",{children:n("dailyReportDesc","ملخص العمليات اليومية")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:(new Date).toLocaleDateString("ar"===N?"ar-DZ":"fr"===N?"fr-FR":"en-US")}),t.jsx("small",{children:n("today","اليوم")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card advanced-card",children:[t.jsx("div",{className:"report-icon",children:"📊"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("advancedReports","تقارير متقدمة")}),t.jsx("p",{children:n("advancedReportsDesc","تقارير مفصلة مع فلاتر متقدمة")}),t.jsxs("div",{className:"report-controls",children:[t.jsxs("div",{className:"date-selector",children:[t.jsx("label",{children:n("reportType","نوع التقرير:")}),t.jsxs("select",{value:Pn.reportType,onChange:e=>Pn.setReportType(e.target.value),children:[t.jsx("option",{value:"daily",children:n("daily","يومي")}),t.jsx("option",{value:"monthly",children:n("monthly","شهري")})]})]}),"daily"===Pn.reportType?t.jsxs("div",{className:"date-selector",children:[t.jsx("label",{children:n("date","التاريخ:")}),t.jsx("input",{type:"date",value:Pn.selectedDate,onChange:e=>Pn.setSelectedDate(e.target.value)})]}):t.jsxs("div",{className:"date-selector",children:[t.jsx("label",{children:n("month","الشهر:")}),t.jsx("input",{type:"month",value:Pn.selectedMonth,onChange:e=>Pn.setSelectedMonth(e.target.value)})]}),t.jsxs("button",{className:"btn btn-primary btn-sm",onClick:Pn.printReport,children:["🖨️ ",n("print","طباعة")]})]})]})]}),t.jsxs("div",{className:"report-card",onClick:()=>(console.log("🚀 Starting NEW CLEAN monthly report generation..."),void h(Ie,Mn,at,Gt,Jt,N)),children:[t.jsx("div",{className:"report-icon",children:"📆"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("monthlyReport","التقرير الشهري")}),t.jsx("p",{children:n("monthlyReportDesc","ملخص العمليات الشهرية")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:n(`month${(new Date).getMonth()+1}`,v((new Date).getMonth()))}),t.jsx("small",{children:n("currentMonth","الشهر الحالي")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(console.log("🚀 Starting NEW CLEAN annual report generation..."),void u(Ie,Mn,at,Gt,Jt,N)),children:[t.jsx("div",{className:"report-icon",children:"🗓️"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("yearlyReport","التقرير السنوي")}),t.jsx("p",{children:n("yearlyReportDesc","ملخص العمليات السنوية")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:(new Date).getFullYear()}),t.jsx("small",{children:n("currentYear","السنة الحالية")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===N,t="ar"===N?"ar":"fr"===N?"fr":"en",a=Ie.reduce(((e,t)=>e+t.finalTotal),0),s=a-d(Ie,Mn).totalCost-at.reduce(((e,t)=>e+parseFloat(t.amount)),0),r=a>0?(s/a*100).toFixed(2):0,i=new Date,o=i.getMonth(),l=i.getFullYear(),c=i.toISOString().split("T")[0],m=Ie.filter((e=>e.date===c));d(m,Mn).totalCost,at.filter((e=>e.date===c)).reduce(((e,t)=>e+parseFloat(t.amount)),0);const h=m.reduce(((e,t)=>e+t.finalTotal),0),u=Ie.filter((e=>{const t=new Date(e.date);return t.getMonth()===o&&t.getFullYear()===l}));d(u,Mn).totalCost,at.filter((e=>{const t=new Date(e.date);return t.getMonth()===o&&t.getFullYear()===l})).reduce(((e,t)=>e+parseFloat(t.amount)),0);const p=u.reduce(((e,t)=>e+t.finalTotal),0),x=[...new Set(Ie.map((e=>e.customerName)))].length,g=Ie.length>0?a/Ie.length:0,v=[...new Set(Ie.filter((e=>"دين"===e.paymentMethod)).map((e=>e.customerName)))].length,b=Ie.filter((e=>"دين"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),f=Mn.reduce(((e,t)=>e+t.stock*t.buyPrice),0),j=Mn.filter((e=>e.stock<=e.minStock)).length,y=Mn.filter((e=>0===e.stock)).length,w=f>0?(a/f).toFixed(2):0,$=0===o?11:o-1,S=0===o?l-1:l,k=Ie.filter((e=>{const t=new Date(e.date);return t.getMonth()===$&&t.getFullYear()===S})).reduce(((e,t)=>e+t.finalTotal),0),C=k>0?((p-k)/k*100).toFixed(1):0,D=Ie.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0);Ie.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0);const I=a>0?(D/a*100).toFixed(1):0,M=Ie.length>0?a/[...new Set(Ie.map((e=>e.date)))].length:0;Ie.length,Math.max([...new Set(Ie.map((e=>e.date)))].length,1);const T=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("performanceIndicators","تقرير مؤشرات الأداء الرئيسية")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .kpi-dashboard { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; text-align: center; }\n          .kpi-dashboard h2 { margin: 0 0 20px 0; font-size: 32px; }\n          .kpi-overview { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }\n          .kpi-card { background: rgba(255,255,255,0.15); padding: 20px; border-radius: 10px; backdrop-filter: blur(10px); }\n          .kpi-card h4 { margin: 0 0 10px 0; font-size: 14px; opacity: 0.9; }\n          .kpi-card .value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }\n          .kpi-card .trend { font-size: 12px; opacity: 0.8; }\n          .kpi-section { background: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .kpi-section h3 { color: #2c3e50; margin: 0 0 20px 0; font-size: 22px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }\n          .kpi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }\n          .metric-card { background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; position: relative; overflow: hidden; }\n          .metric-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #3498db, #2ecc71); }\n          .metric-card h4 { margin: 0 0 10px 0; color: #34495e; font-size: 16px; }\n          .metric-card .metric-value { font-size: 28px; font-weight: bold; color: #2c3e50; margin-bottom: 8px; }\n          .metric-card .metric-desc { font-size: 14px; color: #7f8c8d; }\n          .metric-card .metric-icon { font-size: 40px; margin-bottom: 15px; }\n          .performance-indicator { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; margin-top: 8px; }\n          .indicator-excellent { background: #d5f4e6; color: #27ae60; }\n          .indicator-good { background: #fff3cd; color: #f39c12; }\n          .indicator-warning { background: #f8d7da; color: #e74c3c; }\n          .insights-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; }\n          .insights-section h3 { margin: 0 0 20px 0; font-size: 22px; }\n          .insights-list { list-style: none; padding: 0; margin: 0; }\n          .insights-list li { background: rgba(255,255,255,0.1); padding: 15px; margin-bottom: 10px; border-radius: 8px; border-left: 4px solid rgba(255,255,255,0.3); }\n          .insights-list li strong { color: #fff; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .kpi-section, .insights-section { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>🎯 ${n("kpiDashboard","تقرير مؤشرات الأداء الرئيسية (KPI)")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("performanceOverview","لوحة قيادة الأداء")}</p>\n        </div>\n\n        <div class="kpi-dashboard">\n          <h2>📊 ${n("performanceOverview","نظرة عامة على الأداء")}</h2>\n          <div class="kpi-overview">\n            <div class="kpi-card">\n              <h4>${n("totalSales","إجمالي المبيعات")}</h4>\n              <div class="value">${Gt(a)}</div>\n              <div class="trend">${n("allPeriods","جميع الفترات")}</div>\n            </div>\n            <div class="kpi-card">\n              <h4>${n("netProfit","صافي الربح")}</h4>\n              <div class="value">${Gt(s)}</div>\n              <div class="trend">${n("margin","هامش")} ${r}%</div>\n            </div>\n            <div class="kpi-card">\n              <h4>${n("todaySales","مبيعات اليوم")}</h4>\n              <div class="value">${Gt(h)}</div>\n              <div class="trend">${m.length} ${n("invoice","فاتورة")}</div>\n            </div>\n            <div class="kpi-card">\n              <h4>${n("monthlySales","مبيعات الشهر")}</h4>\n              <div class="value">${Gt(p)}</div>\n              <div class="trend">${n("growth","نمو")} ${C}%</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="kpi-section">\n          <h3>💰 ${n("financialPerformance","المؤشرات المالية")}</h3>\n          <div class="kpi-grid">\n            <div class="metric-card">\n              <div class="metric-icon">💵</div>\n              <h4>${n("averageOrderValue","متوسط قيمة الطلب")}</h4>\n              <div class="metric-value">${Gt(g)}</div>\n              <div class="metric-desc">${n("averageInvoice","متوسط قيمة الفاتورة")}</div>\n              <div class="performance-indicator ${g>1e4?"indicator-excellent":g>5e3?"indicator-good":"indicator-warning"}">\n                ${g>1e4?n("excellent","ممتاز"):g>5e3?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">📈</div>\n              <h4>${n("profitMargin","هامش الربح")}</h4>\n              <div class="metric-value">${r}%</div>\n              <div class="metric-desc">${n("profitFromSales","نسبة الربح من المبيعات")}</div>\n              <div class="performance-indicator ${r>30?"indicator-excellent":r>15?"indicator-good":"indicator-warning"}">\n                ${r>30?n("excellent","ممتاز"):r>15?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">💳</div>\n              <h4>${n("cashRatio","نسبة المبيعات النقدية")}</h4>\n              <div class="metric-value">${I}%</div>\n              <div class="metric-desc">${n("cashVsCredit","المبيعات النقدية مقابل الآجلة")}</div>\n              <div class="performance-indicator ${I>70?"indicator-excellent":I>50?"indicator-good":"indicator-warning"}">\n                ${I>70?n("excellent","ممتاز"):I>50?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">📊</div>\n              <h4>${n("averageDailySales","متوسط المبيعات اليومية")}</h4>\n              <div class="metric-value">${Gt(M)}</div>\n              <div class="metric-desc">${n("salesVelocity","معدل المبيعات في اليوم")}</div>\n              <div class="performance-indicator ${M>5e4?"indicator-excellent":M>25e3?"indicator-good":"indicator-warning"}">\n                ${M>5e4?n("excellent","ممتاز"):M>25e3?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class="kpi-section">\n          <h3>👥 ${n("customerMetrics","مؤشرات العملاء")}</h3>\n          <div class="kpi-grid">\n            <div class="metric-card">\n              <div class="metric-icon">👤</div>\n              <h4>${n("uniqueCustomers","إجمالي العملاء")}</h4>\n              <div class="metric-value">${x}</div>\n              <div class="metric-desc">${n("uniqueCustomersDesc","عدد العملاء الفريدين")}</div>\n              <div class="performance-indicator ${x>100?"indicator-excellent":x>50?"indicator-good":"indicator-warning"}">\n                ${x>100?n("largeCustomerBase","قاعدة عملاء كبيرة"):x>50?n("mediumCustomerBase","قاعدة عملاء متوسطة"):n("smallCustomerBase","قاعدة عملاء صغيرة")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">💳</div>\n              <h4>${n("customersWithDebt","العملاء المدينون")}</h4>\n              <div class="metric-value">${v}</div>\n              <div class="metric-desc">${n("customersWithDebtDesc","عدد العملاء الذين لديهم ديون")}</div>\n              <div class="performance-indicator ${v/x*100<20?"indicator-excellent":v/x*100<40?"indicator-good":"indicator-warning"}">\n                ${v/x*100<20?n("lowRatio","نسبة منخفضة"):v/x*100<40?n("mediumRatio","نسبة متوسطة"):n("highRatio","نسبة عالية")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">💰</div>\n              <h4>${n("totalDebt","إجمالي الديون")}</h4>\n              <div class="metric-value">${Gt(b)}</div>\n              <div class="metric-desc">${n("totalDebtDesc","المبالغ المستحقة من العملاء")}</div>\n              <div class="performance-indicator ${b/a*100<10?"indicator-excellent":b/a*100<25?"indicator-good":"indicator-warning"}">\n                ${b/a*100<10?n("lowRatio","نسبة منخفضة"):b/a*100<25?n("mediumRatio","نسبة متوسطة"):n("highRatio","نسبة عالية")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">🔄</div>\n              <h4>${n("purchaseFrequency","معدل تكرار الشراء")}</h4>\n              <div class="metric-value">${(Ie.length/x).toFixed(1)}</div>\n              <div class="metric-desc">${n("averageOrdersPerCustomer","متوسط الطلبات لكل عميل")}</div>\n              <div class="performance-indicator ${Ie.length/x>5?"indicator-excellent":Ie.length/x>3?"indicator-good":"indicator-warning"}">\n                ${Ie.length/x>5?n("highLoyalty","ولاء عالي"):Ie.length/x>3?n("mediumLoyalty","ولاء متوسط"):n("lowLoyalty","ولاء منخفض")}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class="kpi-section">\n          <h3>📦 ${n("inventoryMetrics","مؤشرات المخزون")}</h3>\n          <div class="kpi-grid">\n            <div class="metric-card">\n              <div class="metric-icon">💎</div>\n              <h4>${n("inventoryValue","قيمة المخزون")}</h4>\n              <div class="metric-value">${Gt(f)}</div>\n              <div class="metric-desc">${n("totalGoodsValue","إجمالي قيمة البضائع")}</div>\n              <div class="performance-indicator indicator-good">${n("inventoryInvestment","استثمار المخزون")}</div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">⚠️</div>\n              <h4>${n("lowStockItems","المنتجات منخفضة المخزون")}</h4>\n              <div class="metric-value">${j}</div>\n              <div class="metric-desc">${n("needsRestocking","منتجات تحتاج إعادة تموين")}</div>\n              <div class="performance-indicator ${j<5?"indicator-excellent":j<15?"indicator-good":"indicator-warning"}">\n                ${j<5?n("goodStock","مخزون جيد"):j<15?n("needsMonitoring","يحتاج متابعة"):n("urgentRestocking","يحتاج تموين عاجل")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">❌</div>\n              <h4>${n("outOfStockItems","المنتجات نفدت")}</h4>\n              <div class="metric-value">${y}</div>\n              <div class="metric-desc">${n("unavailableProducts","منتجات غير متوفرة")}</div>\n              <div class="performance-indicator ${0===y?"indicator-excellent":y<5?"indicator-good":"indicator-warning"}">\n                ${0===y?n("none","لا توجد"):y<5?n("few","قليلة"):n("many","كثيرة")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">🔄</div>\n              <h4>${n("inventoryTurnover","معدل دوران المخزون")}</h4>\n              <div class="metric-value">${w}</div>\n              <div class="metric-desc">${n("stockSalesSpeed","سرعة بيع المخزون")}</div>\n              <div class="performance-indicator ${w>4?"indicator-excellent":w>2?"indicator-good":"indicator-warning"}">\n                ${w>4?n("fastTurnover","دوران سريع"):w>2?n("mediumTurnover","دوران متوسط"):n("slowTurnover","دوران بطيء")}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class="insights-section">\n          <h3>💡 ${n("insightsAndRecommendations","رؤى الأداء والتوصيات")}</h3>\n          <ul class="insights-list">\n            <li><strong>${n("financialPerformance","الأداء المالي")}:</strong> ${r>20?n("excellentProfitMargin","هامش ربح ممتاز يدل على كفاءة التسعير"):n("improveProfitMargin","يمكن تحسين هامش الربح بمراجعة التكاليف والأسعار")}</li>\n            <li><strong>${n("customerManagement","إدارة العملاء")}:</strong> ${v/x*100<30?n("goodDebtManagement","إدارة جيدة للديون والائتمان"):n("improveCreditPolicies","يحتاج تحسين سياسات الائتمان ومتابعة التحصيل")}</li>\n            <li><strong>${n("inventoryManagement","إدارة المخزون")}:</strong> ${j<10?n("appropriateStockLevel","مستوى مخزون مناسب"):n("improveInventoryPlanning","يحتاج تحسين تخطيط المخزون وإعادة التموين")}</li>\n            <li><strong>${n("growth","النمو")}:</strong> ${C>0?`${n("positiveGrowth","نمو إيجابي")} ${C}% ${n("thisMonth","هذا الشهر")}`:n("needsGrowthStrategies","يحتاج استراتيجيات لتحفيز النمو")}</li>\n            <li><strong>${n("cashFlow","التدفق النقدي")}:</strong> ${I>60?n("healthyCashFlow","تدفق نقدي صحي مع نسبة مبيعات نقدية جيدة"):n("improveCashFlow","يحتاج تحسين التدفق النقدي وتقليل المبيعات الآجلة")}</li>\n          </ul>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,P=window.open("","_blank","width=1200,height=800");P.document.write(T),P.document.close(),P.onload=()=>setTimeout((()=>P.print()),1e3),Jt(n("kpiReportOpened","🎯 تم فتح تقرير مؤشرات الأداء للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"🎯"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("kpiReport","مؤشرات الأداء")}),t.jsx("p",{children:n("kpiReportDesc","KPIs ومقاييس الأداء")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:"KPI"}),t.jsx("small",{children:n("analysis","تحليل")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]})]}),Pn.reportData&&t.jsxs("div",{className:"advanced-report-display",children:[t.jsxs("h2",{children:["📊 ","daily"===Pn.reportData.type?n("dailyReport","التقرير اليومي"):n("monthlyReport","التقرير الشهري")]}),t.jsx("p",{className:"report-date",children:"daily"===Pn.reportData.type?`${n("date","تاريخ")}: ${new Date(Pn.reportData.date).toLocaleDateString("ar"===N?"ar-DZ":"fr"===N?"fr-FR":"en-US")}`:`${n("month","شهر")}: ${Pn.reportData.date}`}),t.jsxs("div",{className:"report-summary-cards",children:[t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("totalSales","إجمالي المبيعات")}),t.jsx("div",{className:"summary-value",children:Gt(Pn.reportData.totalSales)})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:n("netProfit","صافي الربح")}),t.jsx("div",{className:"summary-value",children:Gt(Pn.reportData.netProfit)})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("profitMargin","هامش الربح")}),t.jsxs("div",{className:"summary-value",children:[Pn.reportData.profitMargin.toFixed(2),"%"]})]}),t.jsxs("div",{className:"summary-card purple",children:[t.jsx("h3",{children:n("invoiceCount","عدد الفواتير")}),t.jsx("div",{className:"summary-value",children:Pn.reportData.invoicesCount})]})]}),Pn.reportData.soldProducts.length>0&&t.jsxs("div",{className:"best-products-section",children:[t.jsxs("h3",{children:["🏆 ",n("topProducts","أفضل المنتجات مبيعاً")]}),t.jsx("div",{className:"products-table-container",children:t.jsxs("table",{className:"products-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("product","المنتج")}),t.jsx("th",{children:n("category","الفئة")}),t.jsx("th",{children:n("quantitySold","الكمية المباعة")}),t.jsx("th",{children:n("totalRevenue","إجمالي الإيرادات")}),t.jsx("th",{children:n("profit","الربح")}),t.jsx("th",{children:n("profitMargin","هامش الربح")})]})}),t.jsx("tbody",{children:Pn.reportData.soldProducts.slice(0,10).map(((e,n)=>t.jsxs("tr",{className:0===n?"best-product":"",children:[t.jsxs("td",{children:[0===n&&t.jsx("span",{className:"crown",children:"👑"}),e.name]}),t.jsx("td",{children:e.category}),t.jsx("td",{children:e.totalQuantity}),t.jsx("td",{children:Gt(e.totalRevenue)}),t.jsx("td",{className:e.profit>0?"profit-positive":"profit-negative",children:Gt(e.profit)}),t.jsxs("td",{children:[e.totalRevenue>0?(e.profit/e.totalRevenue*100).toFixed(2):0,"%"]})]},e.id)))})]})})]})]}),t.jsxs("div",{className:"reports-summary",children:[t.jsxs("h2",{children:["📊 ",n("quickSummary","ملخص سريع")]}),t.jsxs("div",{className:"summary-stats",children:[t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"💰"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("totalSales","إجمالي المبيعات")}),t.jsx("p",{children:Gt(Ie.reduce(((e,t)=>e+t.finalTotal),0))})]})]}),t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"📦"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("totalPurchases","إجمالي المشتريات")}),t.jsx("p",{children:Gt(et.reduce(((e,t)=>e+t.finalTotal),0))})]})]}),t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"💎"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("netProfit","صافي الربح")}),t.jsx("p",{children:(()=>{const e=Ie.reduce(((e,t)=>e+t.finalTotal),0),t=d(Ie,Mn).totalCost,n=at.reduce(((e,t)=>e+(parseFloat(t.amount)||0)),0);return Gt(e-t-n)})()})]})]}),t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"📊"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("inventoryValue","قيمة المخزون")}),t.jsx("p",{children:Gt(Mn.reduce(((e,t)=>e+t.stock*t.buyPrice),0))})]})]})]})]})]}),"reports"===J&&"مدير"!==zt.role&&"admin"!==zt.role&&t.jsx("div",{className:"access-denied-page",children:t.jsxs("div",{className:"access-denied-content",children:[t.jsx("div",{className:"access-denied-icon",children:"🔒"}),t.jsx("h1",{children:n("accessRestricted","الوصول مقيد")}),t.jsx("p",{children:n("reportsManagerOnly","التقارير والإحصائيات متاحة للمدير فقط")}),t.jsx("p",{children:n("loginAsManager","يرجى تسجيل الدخول بحساب المدير للوصول إلى هذه الصفحة")}),t.jsx("button",{className:"btn btn-primary",onClick:()=>_("dashboard"),children:n("backToDashboard","العودة للوحة التحكم")})]})}),"settings"===J&&t.jsxs("div",{className:`settings-page lang-${N}`,children:[t.jsxs("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsx("h1",{children:n("settings","الإعدادات")})}),t.jsx("div",{className:"page-description-section",children:t.jsx("p",{children:n("storeAndSellersManagement","إدارة إعدادات المتجر والبائعين")})})]}),t.jsxs("div",{className:"settings-tabs",children:[t.jsxs("div",{className:"settings-section",children:[t.jsxs("div",{className:"section-header "+("ar"!==N?"section-header-ltr":""),children:[t.jsxs("h2",{children:["🏪 ",n("storeSettings","إعدادات المتجر")]}),t.jsxs("button",{className:"btn btn-primary",onClick:()=>{jt(!0)},children:["⚙️ ",n("editSettings","تعديل الإعدادات")]})]}),t.jsxs("div",{className:"settings-cards",children:[t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"🏪"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("storeName","اسم المتجر")}),t.jsx("p",{children:Et.storeName})]})]}),t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"📞"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("phoneNumber","رقم الهاتف")}),t.jsx("p",{children:Et.storePhone})]})]}),t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"📍"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("address","العنوان")}),t.jsx("p",{children:Et.storeAddress})]})]}),t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"💰"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("taxRate","معدل الضريبة")}),t.jsxs("p",{children:[Et.taxRate,"%"]})]})]})]})]}),t.jsxs("div",{className:"settings-section",children:[t.jsxs("div",{className:"section-header "+("ar"!==N?"section-header-ltr":""),children:[t.jsxs("h2",{children:["👥 ",n("sellersManagement","إدارة البائعين")]}),t.jsxs("button",{className:"btn btn-success",onClick:()=>{qt({id:`S${String(Rt.length+1).padStart(3,"0")}`,name:"",username:"",password:"",phone:"",email:"",role:"seller",isActive:!0}),Lt(!0)},children:["+ ",n("addNewSeller","إضافة بائع جديد")]})]}),Rt.length>0&&t.jsxs("div",{className:"bulk-actions "+(Tt.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:Rt.length>0&&Tt.length===Rt.length,onChange:()=>Wt("sellers",Rt),id:"select-all-sellers"}),t.jsx("label",{htmlFor:"select-all-sellers",className:"select-all-label",children:n("selectAll","تحديد الكل")}),Tt.length>0&&t.jsxs("span",{className:"selected-count",children:["(",Tt.length," ",n("selected","محدد"),")"]})]}),Tt.length>0&&("مدير"===zt.role||"admin"===zt.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Kt("sellers"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",Tt.length,")"]})]}),t.jsx("div",{className:"sellers-table-container",children:t.jsxs("table",{className:"sellers-table "+("ar"!==N?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:Rt.length>0&&Tt.length===Rt.length,onChange:()=>Wt("sellers",Rt),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("sellerNumber","الرقم")}),t.jsx("th",{children:n("sellerName","الاسم")}),t.jsx("th",{children:n("username","اسم المستخدم")}),t.jsx("th",{children:n("phone","الهاتف")}),t.jsx("th",{children:n("role","الدور")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("creationDate","تاريخ الإنشاء")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:Rt.map((e=>t.jsxs("tr",{className:Tt.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:Tt.includes(e.id),onChange:()=>Yt("sellers",e.id)})}),t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.username}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:t.jsx("span",{className:`role-badge ${e.role}`,children:"admin"===e.role?n("admin","مدير"):n("seller","بائع")})}),t.jsx("td",{children:t.jsx("span",{className:"status-badge "+(e.isActive?"active":"inactive"),children:e.isActive?n("active","نشط"):n("inactive","غير نشط")})}),t.jsx("td",{children:new Date(e.createdAt).toLocaleDateString("ar"===N?"ar-DZ":"fr"===N?"fr-FR":"en-US")}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons",children:[t.jsx("button",{className:"btn-icon "+(e.isActive?"pause":"play"),onClick:()=>(e=>{const t=Rt.map((t=>t.id===e?{...t,isActive:!t.isActive}:t));tn(t),Jt(`✅ ${n("sellerStatusUpdated","تم تحديث حالة البائع")}`,"success",2e3)})(e.id),title:e.isActive?n("deactivate","إيقاف"):n("activate","تفعيل"),children:e.isActive?"⏸️":"▶️"}),"admin"!==e.role&&t.jsx("button",{className:"btn-icon delete",onClick:()=>(e=>{if(window.confirm(n("confirmDeleteSeller","هل أنت متأكد من حذف هذا البائع؟"))){const t=Rt.filter((t=>t.id!==e));tn(t),Jt(`🗑️ ${n("sellerDeletedSuccess","تم حذف البائع بنجاح")}`,"success",2e3)}})(e.id),title:n("delete","حذف"),children:"🗑️"})]})})]},e.id)))})]})})]})]})]})]}),ee&&(console.log("🛒 Sales Modal Rendering - Current Invoice State:",oa),console.log("🛒 Sales Modal - Items Count:",oa.items.length),console.log("🛒 Sales Modal - Items:",oa.items),!0)&&t.jsx("div",{className:"modal-overlay",onClick:Qa,children:t.jsxs("div",{className:`modal-content sales-modal-landscape lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("div",{className:"modal-title-section",children:t.jsxs("h2",{children:["🛒 ",n("newSalesInvoice","فاتورة مبيعات جديدة")]})}),t.jsx("button",{className:"modal-close",onClick:Xa,children:"×"})]}),t.jsxs("div",{className:"sales-invoice-landscape",children:[t.jsx("div",{className:"invoice-header-section",children:t.jsx("div",{className:"customer-info",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("customer","الزبون")}),t.jsxs("select",{value:oa.customerId,onChange:e=>{const t=e.target.value;if("GUEST"===t){const e=oa.items.reduce(((e,t)=>e+t.total),0),t=e*(Et.taxRate/100),n=e+t-0;la({...oa,customerId:"GUEST",customerName:"زبون عابر",discount:0,total:e,tax:t,finalTotal:n})}else{const e=fa.find((e=>e.id===t));if(e){const a=oa.items.reduce(((e,t)=>e+t.total),0),s=oa.items.reduce(((e,t)=>{const n=Mn.find((e=>e.id===t.productId));if(n&&n.buyPrice&&n.sellPrice){return e+(n.sellPrice-n.buyPrice)*t.quantity}return e}),0)*(e.discountPercentage||0)/100,r=a*(Et.taxRate/100),i=a+r-s;la({...oa,customerId:t,customerName:e.name,discount:s,total:a,tax:r,finalTotal:i}),e.discountPercentage>0&&Jt(`💸 ${n("discountApplied","تم تطبيق خصم")} ${e.discountPercentage}% ${n("fromProfitMarginForCustomer","من هامش الربح للزبون")} ${e.name} (${Gt(s)})`,"success",4e3)}}},children:[t.jsx("option",{value:"GUEST",children:n("walkInCustomer","زبون عابر")}),t.jsx("option",{value:"",children:n("selectRegisteredCustomer","اختر زبون مسجل")}),fa.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",e.phone]},e.id)))]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentMethod","طريقة الدفع")}),t.jsxs("select",{value:oa.paymentMethod,onChange:e=>la({...oa,paymentMethod:e.target.value}),children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"دين",children:n("credit","دين")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("input",{type:"text",value:oa.invoiceNumber,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:oa.date,onChange:e=>la({...oa,date:e.target.value})})]})]})})}),t.jsxs("div",{className:"sales-barcode-scanner-row",children:[t.jsxs("div",{className:"scanner-input-row",children:[t.jsxs("h3",{className:"scanner-title",children:["📷 ",t.jsx("span",{className:"scanner-status-active",children:n("active","Actif")})," - ",n("scanBarcode","Scanner le code-barres")," 📷"]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanToAddProduct","امسح الباركود لإضافة منتج"),value:Ua,onChange:e=>{if(!ee||Zn)return void console.log("🚫 Sales scanner BLOCKED - sales modal not open or edit modal is open");const t=e.target.value,a=$(t);qa(a),console.log("🛒 Enhanced sales scanner input:",{raw:t,cleaned:a,scannerTool:"Enhanced for barcode tools"}),a.length>=3?(clearTimeout(window.salesScannerTimeout),window.salesScannerTimeout=setTimeout((()=>{const e=Mn.find((e=>e.barcode===a&&""!==a.trim()));e?(console.log("🛒 Sales: Auto-detected product from scanner tool:",e.name),os(a),qa("")):console.log("🛒 Sales: Product not found for code:",a)}),150)):a.length>0&&a.length<3&&(clearTimeout(window.salesScannerValidationTimeout),window.salesScannerValidationTimeout=setTimeout((()=>{Ua===a&&a.length>0&&a.length<3&&Jt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}),1e3))},onKeyDown:e=>{if(ee&&!Zn){if("Enter"===e.key&&"keydown"===e.type){e.preventDefault(),e.stopPropagation();const t=e.target.value.trim();console.log("🛒 Sales Enter pressed with code:",t),t.length>=3?(qa(""),clearTimeout(window.salesScannerValidationTimeout)):t.length>0&&Jt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}}else console.log("🚫 Sales scanner keypress BLOCKED - sales modal not open or edit modal is open")},onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1),console.log("🔧 BARCODE FIX: Sales scanner focused - shortcuts disabled"))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Sales scanner blurred - shortcuts re-enabled"))}),100)},className:"barcode-input",ref:Za,autoFocus:!0}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{qa("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]})]})]}),t.jsx("div",{className:"lcd-display-row",children:t.jsx("div",{className:"lcd-screen-big",children:t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Gt(oa.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"})]})})})]}),t.jsx("div",{className:"product-selection-section",children:t.jsx("div",{className:"manual-selection",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group flex-2",children:[t.jsx("label",{children:n("product","المنتج")}),t.jsxs("div",{className:"product-selection-container",children:[t.jsx("input",{type:"text",placeholder:n("searchProductPlaceholder","ابحث عن منتج (الاسم، الرمز، الباركود)..."),className:"search-input product-search",value:xa,onChange:e=>{const t=e.target.value;if(ga(t),t.length>0){const e=Mn.filter((e=>{const n=t.toLowerCase();return e.name.toLowerCase().includes(n)||e.id.toLowerCase().includes(n)||e.barcode&&e.barcode.toLowerCase().includes(n)}));1===e.length?(da(e[0].id),pa(e[0].sellPrice||e[0].price),ha(1)):0===e.length&&da("")}else da("")}}),t.jsxs("select",{value:ca,onChange:e=>{da(e.target.value);const t=Mn.find((t=>t.id===e.target.value));t&&(pa(t.sellPrice||t.price),ha(1))},children:[t.jsx("option",{value:"",children:n("selectProduct","اختر منتج")}),ns.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",Gt(e.sellPrice||e.price)," - ",n("available","متوفر"),": ",e.stock]},e.id)))]})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("quantity","الكمية")}),t.jsx("input",{type:"number",value:ma,onChange:e=>ha(parseInt(e.target.value)||1),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("price","السعر")}),t.jsx("input",{type:"number",value:ua,onChange:e=>pa(parseFloat(e.target.value)||0),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:" "}),t.jsxs("button",{type:"button",className:"btn btn-success btn-add",onClick:ts,disabled:!ca,children:["➕ ",n("add","إضافة")]})]})]})})}),t.jsxs("div",{className:"invoice-content-section",children:[t.jsxs("div",{className:"items-section",children:[t.jsxs("h3",{children:["🛍️ ",n("invoiceItems","عناصر الفاتورة")]}),0===oa.items.length?t.jsxs("div",{className:"no-items",children:[t.jsx("p",{children:n("noProductsAdded","لم يتم إضافة أي منتجات بعد")}),t.jsx("p",{children:n("selectProductsFromList","اختر المنتجات من القائمة أعلاه")})]}):t.jsx("div",{className:"items-table",children:t.jsxs("table",{className:""+("ar"!==N?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("productName","المنتج")}),t.jsx("th",{children:n("quantity","الكمية")}),t.jsx("th",{children:n("price","السعر")}),t.jsx("th",{children:n("total","المجموع")}),t.jsx("th",{children:n("action","إجراء")})]})}),t.jsx("tbody",{children:oa.items.map(((e,a)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.quantity,onChange:t=>{const s=parseInt(t.target.value)||1,r=Mn.find((t=>t.id===e.productId));if(r&&s<=r.stock){const t=[...oa.items];t[a].quantity=s,t[a].total=s*e.price;const n=t.reduce(((e,t)=>e+t.total),0),r=n*(Et.taxRate/100),i=n+r-oa.discount;la({...oa,items:t,total:n,tax:r,finalTotal:i})}else Jt(`❌ ${n("quantityRequiredExceedsStock","الكمية المطلوبة أكبر من المتوفر")} (${r?.stock||0})`,"error",3e3)},min:"1",className:"quantity-input"})}),t.jsx("td",{children:Gt(e.price)}),t.jsx("td",{children:Gt(e.total)}),t.jsx("td",{children:t.jsx("button",{className:"btn-delete",onClick:()=>(e=>{const t=oa.items.find((t=>t.productId===e||t.productId===String(e)||String(t.productId)===String(e))),a=oa.items.filter((t=>t.productId!==e&&t.productId!==String(e)&&String(t.productId)!==String(e))),s=a.reduce(((e,t)=>e+t.total),0),r=s*(Et.taxRate/100),i=s+r-oa.discount;la({...oa,items:a,total:s,tax:r,finalTotal:i}),b.play("deleteProduct",{showNotification:!1}),t&&Jt(`🗑️ ${n("productRemovedFromInvoice","تم حذف")} ${t.productName} ${n("fromInvoice","من الفاتورة")}`,"info",2e3)})(e.productId),title:n("deleteTitle","حذف"),children:"🗑️"})})]},a)))})]})})]}),t.jsx("div",{className:"totals-section",children:t.jsxs("div",{className:"totals-grid",children:[t.jsxs("div",{className:"discount-input",children:[t.jsxs("label",{children:["💸 ",n("discount","الخصم")]}),t.jsx("input",{type:"number",value:oa.discount,onChange:e=>{const t=parseFloat(e.target.value)||0,n=oa.items.reduce(((e,t)=>e+t.total),0),a=n*(Et.taxRate/100),s=n+a-t;la({...oa,discount:t,total:n,tax:a,finalTotal:s})},step:"0.01"})]}),t.jsxs("div",{className:"totals-summary",children:[t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("subtotal","المجموع الفرعي"),":"]}),t.jsx("span",{children:Gt(oa.total)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("tax","الضريبة")," (",Et.taxRate,"%):"]}),t.jsx("span",{children:Gt(oa.tax)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("discount","الخصم"),":"]}),t.jsx("span",{children:Gt(oa.discount)})]}),t.jsxs("div",{className:"total-row final",children:[t.jsxs("span",{children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("span",{children:Gt(oa.finalTotal)})]})]}),t.jsxs("div",{className:"action-buttons "+("ar"!==N?"action-buttons-ltr":""),children:[t.jsxs("button",{className:"btn btn-primary",onClick:ss,children:["💾 ",n("saveInvoice","حفظ الفاتورة")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:Qa,children:["❌ ",n("cancel","إلغاء")]})]})]})})]})]})]})}),ne&&t.jsx("div",{className:"modal-overlay",onClick:jn,children:t.jsxs("div",{className:`modal-content purchase-modal-landscape lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("div",{className:"modal-title-section",children:t.jsxs("h2",{children:["📦 ",He?n("editPurchaseInvoiceTitle","تعديل فاتورة المشتريات"):n("newPurchaseInvoice","فاتورة مشتريات جديدة")]})}),t.jsx("button",{className:"modal-close",onClick:jn,children:"×"})]}),t.jsxs("div",{className:"modal-body purchase-modal-body",children:[t.jsx("div",{className:"invoice-header-section",children:t.jsx("div",{className:"invoice-info-grid",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplier","المورد")}),t.jsxs("select",{value:Ye.supplierId,onChange:e=>{const t=e.target.value;if("NEW_SUPPLIER"===t)on();else if("GENERAL"===t)Ke({...Ye,supplierId:"GENERAL",supplierName:n("generalSupplier","مورد عام")});else{const e=Qe.find((e=>e.id===t));e&&Ke({...Ye,supplierId:t,supplierName:e.name})}},children:[t.jsx("option",{value:"GENERAL",children:n("generalSupplier","مورد عام")}),t.jsx("option",{value:"",children:n("selectRegisteredSupplier","اختر مورد مسجل")}),Qe.map((e=>t.jsxs("option",{value:e.id,children:[Zt(e.name)," - ",e.phone]},e.id))),t.jsx("option",{value:"NEW_SUPPLIER",children:n("addNewSupplier","+ إضافة مورد جديد")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentMethod","طريقة الدفع")}),t.jsxs("select",{value:Ye.paymentMethod,onChange:e=>Ke({...Ye,paymentMethod:e.target.value}),children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"دين",children:n("credit","دين")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("input",{type:"text",value:Ye.invoiceNumber,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:Ye.date,onChange:e=>Ke({...Ye,date:e.target.value})})]})]})})}),t.jsx("div",{className:"product-selection-section",children:t.jsx("div",{className:"manual-selection",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group flex-2",children:[t.jsx("label",{children:n("product","المنتج")}),t.jsxs("select",{value:ca,onChange:e=>{if("NEW_PRODUCT"===e.target.value)hs(),Jt("📦 "+n("addNewProductOpened","تم فتح نافذة إضافة منتج جديد"),"success",2e3);else{da(e.target.value);const t=Mn.find((t=>t.id===e.target.value));t&&(pa(t.buyPrice||0),ha(1))}},children:[t.jsx("option",{value:"",children:n("selectProduct","اختر منتج")}),Mn.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",Gt(e.buyPrice||0)," - ",n("available","متوفر"),": ",e.stock]},e.id))),t.jsxs("option",{value:"NEW_PRODUCT",children:["📦 ",n("addNewProduct","إضافة منتج جديد")]})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("quantity","الكمية")}),t.jsx("input",{type:"number",value:ma,onChange:e=>ha(parseInt(e.target.value)||1),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("purchasePrice","سعر الشراء")}),t.jsx("input",{type:"number",value:ua,onChange:e=>pa(parseFloat(e.target.value)||0),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:" "}),t.jsxs("button",{type:"button",className:"btn btn-success btn-add",onClick:yn,disabled:!ca,children:["➕ ",n("add","إضافة")]})]})]})})}),t.jsxs("div",{className:"invoice-content-section",children:[t.jsxs("div",{className:"items-section",children:[t.jsxs("h3",{children:["📦 ",n("purchaseInvoiceItems","عناصر فاتورة المشتريات")]}),0===Ye.items.length?t.jsxs("div",{className:"no-items",children:[t.jsx("p",{children:n("noPurchaseProductsAdded","لم يتم إضافة أي منتجات بعد")}),t.jsx("p",{children:n("selectProductsFromList","اختر المنتجات من القائمة أعلاه")})]}):t.jsx("div",{className:"items-table",children:t.jsxs("table",{children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("productName","المنتج")}),t.jsx("th",{children:n("quantity","الكمية")}),t.jsx("th",{children:n("purchasePrice","سعر الشراء")}),t.jsx("th",{children:n("total","المجموع")}),t.jsx("th",{children:n("action","إجراء")})]})}),t.jsx("tbody",{children:Ye.items.map(((e,a)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.quantity,onChange:t=>{const n=parseInt(t.target.value)||1,s=[...Ye.items];s[a].quantity=n,s[a].total=n*e.price;const r=s.reduce(((e,t)=>e+t.total),0),i=r*(Et.taxRate/100),o=r+i-Ye.discount;Ke({...Ye,items:s,total:r,tax:i,finalTotal:o})},min:"1",className:"quantity-input"})}),t.jsx("td",{children:Gt(e.price)}),t.jsx("td",{children:Gt(e.total)}),t.jsx("td",{children:t.jsx("button",{className:"btn-delete",onClick:()=>{const e=Ye.items.filter(((e,t)=>t!==a)),t=e.reduce(((e,t)=>e+t.total),0),n=t*(Et.taxRate/100),s=t+n-Ye.discount;Ke({...Ye,items:e,total:t,tax:n,finalTotal:s})},title:n("delete","حذف"),children:"🗑️"})})]},a)))})]})})]}),t.jsx("div",{className:"totals-section",children:t.jsxs("div",{className:"totals-grid",children:[t.jsxs("div",{className:"discount-input",children:[t.jsxs("label",{children:["💸 ",n("discount","الخصم")]}),t.jsx("input",{type:"number",value:Ye.discount,onChange:e=>{const t=parseFloat(e.target.value)||0,n=Ye.items.reduce(((e,t)=>e+t.total),0),a=n*(Et.taxRate/100),s=n+a-t;Ke({...Ye,discount:t,total:n,tax:a,finalTotal:s})},step:"0.01"})]}),t.jsxs("div",{className:"totals-summary",children:[t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("subtotal","المجموع الفرعي"),":"]}),t.jsx("span",{children:Gt(Ye.total)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("tax","الضريبة")," (",Et.taxRate,"%):"]}),t.jsx("span",{children:Gt(Ye.tax)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("discount","الخصم"),":"]}),t.jsx("span",{children:Gt(Ye.discount)})]}),t.jsxs("div",{className:"total-row final",children:[t.jsxs("span",{children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("span",{children:Gt(Ye.finalTotal)})]})]}),t.jsxs("div",{className:"action-buttons",children:[t.jsxs("button",{className:"btn btn-primary",onClick:Nn,children:["💾 ",He?n("updatePurchaseInvoice","تحديث فاتورة المشتريات"):n("savePurchaseInvoice","حفظ فاتورة المشتريات")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:jn,children:["❌ ",n("cancel","إلغاء")]})]})]})})]})]})]})}),Q&&t.jsx("div",{className:"modal-overlay",onClick:()=>X(!1),children:t.jsxs("div",{className:`modal-content customer-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("h2",{children:"ar"===N?"إضافة زبون جديد":"fr"===N?"Ajouter un nouveau client":"Add New Customer"}),t.jsx("button",{className:"modal-close",onClick:()=>X(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["ar"===N?"اسم الزبون":"fr"===N?"Nom du client":"Customer Name"," *"]}),t.jsx("input",{type:"text",value:Na.name,onChange:e=>wa({...Na,name:e.target.value}),placeholder:"ar"===N?"أدخل اسم الزبون":"fr"===N?"Entrez le nom du client":"Enter customer name",required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===N?"رقم الهاتف":"fr"===N?"Numéro de téléphone":"Phone Number"}),t.jsx("input",{type:"tel",value:Na.phone,onChange:e=>wa({...Na,phone:e.target.value}),placeholder:"ar"===N?"رقم الهاتف":"fr"===N?"Numéro de téléphone":"Phone Number"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===N?"البريد الإلكتروني":"fr"===N?"Adresse e-mail":"Email Address"}),t.jsx("input",{type:"email",value:Na.email,onChange:e=>wa({...Na,email:e.target.value}),placeholder:"ar"===N?"البريد الإلكتروني":"fr"===N?"Adresse e-mail":"Email Address"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===N?"العنوان":"fr"===N?"Adresse":"Address"}),t.jsx("input",{type:"text",value:Na.address,onChange:e=>wa({...Na,address:e.target.value}),placeholder:"ar"===N?"العنوان الكامل":"fr"===N?"Adresse complète":"Full Address"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===N?"الشركة":"fr"===N?"Entreprise":"Company"}),t.jsx("input",{type:"text",value:Na.company,onChange:e=>wa({...Na,company:e.target.value}),placeholder:"ar"===N?"اسم الشركة (اختياري)":"fr"===N?"Nom de l'entreprise (optionnel)":"Company Name (optional)"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===N?"الرصيد الافتتاحي":"fr"===N?"Solde d'ouverture":"Opening Balance"}),t.jsx("input",{type:"number",value:Na.balance,onChange:e=>wa({...Na,balance:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===N?"حد الائتمان":"fr"===N?"Limite de crédit":"Credit Limit"}),t.jsx("input",{type:"number",value:Na.creditLimit,onChange:e=>wa({...Na,creditLimit:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===N?"فترة السداد (بالأيام)":"fr"===N?"Délai de paiement (en jours)":"Payment Term (in days)"}),t.jsx("input",{type:"number",value:Na.paymentTerm,onChange:e=>wa({...Na,paymentTerm:parseInt(e.target.value)||30}),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💸 ","ar"===N?"خصم هامش الربح (%)":"fr"===N?"Remise marge bénéficiaire (%)":"Profit Margin Discount (%)"]}),t.jsx("input",{type:"number",value:Na.discountPercentage,onChange:e=>wa({...Na,discountPercentage:parseFloat(e.target.value)||0}),placeholder:"ar"===N?"نسبة الخصم من هامش الربح":"fr"===N?"Pourcentage de remise sur marge":"Profit margin discount percentage",min:"0",max:"100",step:"0.1"}),t.jsx("small",{style:{color:"#666",fontSize:"12px"},children:"ar"===N?"يتم تطبيق الخصم على هامش الربح وليس إجمالي المبيعات":"fr"===N?"La remise est appliquée à la marge bénéficiaire et non au total des ventes":"Discount is applied to profit margin, not total sales"})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsx("button",{className:"btn btn-primary",onClick:()=>{if(!Na.name.trim())return void Jt(n("pleaseEnterCustomerName","يرجى إدخال اسم الزبون"),"error");const e=Na.name.trim();if(fa.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==Na.id)))return void Jt(`❌ ${n("customerNameAlreadyExists","اسم الزبون موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);if(Na.phone&&""!==Na.phone.trim()){const e=fa.find((e=>e.phone===Na.phone.trim()&&e.id!==Na.id));if(e)return void Jt(`❌ ${n("phoneNumberAlreadyExists","رقم الهاتف موجود بالفعل")}: "${Na.phone}" - ${n("usedByCustomer","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const t=`C${String(fa.length+1).padStart(3,"0")}`,a={...Na,id:t,createdAt:(new Date).toISOString()},s=[...fa,a];ya(s),wa({id:"",name:"",email:"",phone:"",address:"",company:"",balance:0,creditLimit:0,paymentTerm:30,discountPercentage:0,status:"نشط"}),X(!1),Jt(n("customerAddedSuccessfully","تم إضافة الزبون بنجاح"),"success")},children:"ar"===N?"حفظ الزبون":"fr"===N?"Enregistrer le client":"Save Customer"}),t.jsx("button",{className:"btn btn-secondary",onClick:()=>X(!1),children:"ar"===N?"إلغاء":"fr"===N?"Annuler":"Cancel"})]})]})}),$a&&ka&&t.jsx("div",{className:"modal-overlay",onClick:Ba,children:t.jsxs("div",{className:`modal-content customer-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("h2",{children:n("editCustomerData","تعديل بيانات الزبون")}),t.jsx("button",{className:"modal-close",onClick:Ba,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("customerID","رقم الزبون")}),t.jsx("input",{type:"text",value:ka.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("customerNameRequired","اسم الزبون")," *"]}),t.jsx("input",{type:"text",value:ka.name,onChange:e=>Ca({...ka,name:e.target.value}),placeholder:n("enterCustomerName","أدخل اسم الزبون"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("phoneNumber","رقم الهاتف")}),t.jsx("input",{type:"tel",value:ka.phone,onChange:e=>Ca({...ka,phone:e.target.value}),placeholder:n("phoneNumberPlaceholder","رقم الهاتف")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("emailAddress","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:ka.email,onChange:e=>Ca({...ka,email:e.target.value}),placeholder:n("emailPlaceholder","البريد الإلكتروني")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("address","العنوان")}),t.jsx("input",{type:"text",value:ka.address,onChange:e=>Ca({...ka,address:e.target.value}),placeholder:n("addressPlaceholder","العنوان")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("companyName","الشركة")}),t.jsx("input",{type:"text",value:ka.company,onChange:e=>Ca({...ka,company:e.target.value}),placeholder:n("companyPlaceholder","اسم الشركة")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("openingBalance","الرصيد الافتتاحي")}),t.jsx("input",{type:"number",value:ka.balance,onChange:e=>Ca({...ka,balance:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("creditLimit","حد الائتمان")}),t.jsx("input",{type:"number",value:ka.creditLimit,onChange:e=>Ca({...ka,creditLimit:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentTermDays","فترة السداد (بالأيام)")}),t.jsx("input",{type:"number",value:ka.paymentTerm,onChange:e=>Ca({...ka,paymentTerm:parseInt(e.target.value)||30}),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💸 ",n("profitMarginDiscount","خصم هامش الربح (%)")]}),t.jsx("input",{type:"number",value:ka.discountPercentage,onChange:e=>Ca({...ka,discountPercentage:parseFloat(e.target.value)||0}),placeholder:n("discountPercentagePlaceholder","نسبة الخصم من هامش الربح"),min:"0",max:"100",step:"0.1"}),t.jsx("small",{style:{color:"#666",fontSize:"12px"},children:n("discountAppliedToProfit","يتم تطبيق الخصم على هامش الربح وليس إجمالي المبيعات")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("status","الحالة")}),t.jsxs("select",{value:ka.status,onChange:e=>Ca({...ka,status:e.target.value}),children:[t.jsx("option",{value:"نشط",children:n("active","نشط")}),t.jsx("option",{value:"غير نشط",children:n("inactive","غير نشط")})]})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsx("button",{className:"btn btn-primary",onClick:()=>{if(!ka.name.trim())return void Jt(n("pleaseEnterCustomerName","يرجى إدخال اسم الزبون"),"error");const e=ka.name.trim();if(fa.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==ka.id)))return void Jt(`❌ ${n("customerNameAlreadyExists","اسم الزبون موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);if(ka.phone&&""!==ka.phone.trim()){const e=fa.find((e=>e.phone===ka.phone.trim()&&e.id!==ka.id));if(e)return void Jt(`❌ ${n("phoneNumberAlreadyExists","رقم الهاتف موجود بالفعل")}: "${ka.phone}" - ${n("usedByCustomer","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const t=fa.map((e=>e.id===ka.id?ka:e));ya(t),Ba(),Jt(n("customerDataUpdatedSuccessfully","تم تحديث بيانات الزبون بنجاح"),"success")},children:n("saveChanges","حفظ التغييرات")}),t.jsx("button",{className:"btn btn-secondary",onClick:Ba,children:n("cancel","إلغاء")})]})]})}),se&&ie&&t.jsx("div",{className:"modal-overlay",onClick:()=>re(!1),children:t.jsxs("div",{className:`modal-content large-modal lang-${N}`,onClick:e=>e.stopPropagation(),dir:"ar"===N?"rtl":"ltr",children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["📋 ",n("customerOperations","عمليات الزبون")," - ",ie.customer.name]}),t.jsx("button",{className:"modal-close",onClick:()=>re(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"customer-operations",children:[t.jsx("div",{className:"customer-info-summary",children:t.jsxs("div",{className:"info-card",children:[t.jsxs("h3",{children:["👤 ",n("customerInfo","معلومات الزبون")]}),t.jsxs("div",{className:"info-grid",children:[t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("customerNumber","رقم الزبون"),":"]})," ",ie.customer.id]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("customerName","اسم الزبون"),":"]})," ",ie.customer.name]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("phone","الهاتف"),":"]})," ",ie.customer.phone||"-"]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("email","البريد الإلكتروني"),":"]})," ",ie.customer.email||"-"]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("balance","الرصيد"),":"]}),t.jsx("span",{className:ie.customer.balance<=0?"text-success":"text-danger",children:Gt(ie.customer.balance||0)})]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("profitMarginDiscount","خصم هامش الربح"),":"]})," ",ie.customer.discountPercentage||0,"%"]})]})]})}),t.jsxs("div",{className:"operations-section",children:[t.jsxs("h3",{children:["📊 ",n("customerOperations","عمليات الزبون")]}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("operationType","نوع العملية")}),t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("paymentMethod","طريقة الدفع")}),t.jsx("th",{children:n("amount","المبلغ")}),t.jsx("th",{children:n("status","الحالة")})]})}),t.jsx("tbody",{children:ie.allOperations&&0!==ie.allOperations.length?ie.allOperations.map(((e,a)=>t.jsxs("tr",{children:[t.jsx("td",{children:t.jsx("span",{className:`operation-type ${e.type}`,children:"invoice"===e.type?`🧾 ${n("sale","مبيعة")}`:`💰 ${n("payment","دفعة")}`})}),t.jsx("td",{children:e.invoiceNumber}),t.jsx("td",{children:new Date(e.date).toLocaleDateString("ar"===N?"ar-DZ":"fr"===N?"fr-FR":"en-US")}),t.jsx("td",{children:t.jsx("span",{className:"payment-method "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"),children:"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")})}),t.jsx("td",{children:t.jsx("span",{className:"payment"===e.type?"payment-amount":"",children:"payment"===e.type?`-${Gt(Math.abs(e.finalTotal))}`:Gt(e.finalTotal)})}),t.jsx("td",{children:t.jsx("span",{className:"status "+("payment"===e.type?"payment":"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"paid":"pending"),children:"payment"===e.type?n("paymentReceived","دفعة مستلمة"):"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")})})]},`${e.type}-${e.id||e.invoiceNumber}-${a}`))):t.jsx("tr",{children:t.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:n("noOperationsFound","لا توجد عمليات لهذا الزبون")})})})]})})]}),t.jsx("div",{className:"operations-summary",children:t.jsxs("div",{className:"summary-cards",children:[t.jsxs("div",{className:"summary-card green",children:[t.jsx("h4",{children:n("totalOperations","إجمالي العمليات")}),t.jsx("div",{className:"summary-value",children:ie.totalOperations})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h4",{children:n("totalAmount","إجمالي المبلغ")}),t.jsx("div",{className:"summary-value",children:Gt(ie.totalAmount)})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h4",{children:n("cashOperations","العمليات النقدية")}),t.jsxs("div",{className:"summary-value",children:[ie.cashOperations.length," (",Gt(ie.totalCash),")"]})]}),t.jsxs("div",{className:"summary-card red",children:[t.jsx("h4",{children:n("creditOperations","عمليات الدين")}),t.jsxs("div",{className:"summary-value",children:[ie.creditOperations.length," (",Gt(ie.totalCredit),")"]})]})]})})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[ie.customer.balance>0&&t.jsxs("button",{className:"btn btn-success",onClick:()=>{re(!1),Fa(ie.customer)},children:["💰 ",n("payCustomerDebt","تسديد فواتير الزبون")]}),t.jsxs("button",{className:"btn btn-info",onClick:()=>(e=>{const t="ar"===N,a="ar"===N?"ar":"fr"===N?"fr":"en",s=`\n      <!DOCTYPE html>\n      <html dir="${t?"rtl":"ltr"}" lang="${a}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("customerTransactionsReport","تقرير معاملات الزبون")} - ${e.customer.name}</title>\n        <style>\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            direction: ${t?"rtl":"ltr"};\n            margin: 20px;\n            color: #333;\n          }\n          .header {\n            text-align: center;\n            margin-bottom: 30px;\n            border-bottom: 2px solid #3498db;\n            padding-bottom: 20px;\n          }\n          .header h1 {\n            color: #2c3e50;\n            margin: 0;\n            font-size: 28px;\n          }\n          .header p {\n            color: #7f8c8d;\n            margin: 5px 0;\n          }\n          .customer-info {\n            background: #f8f9fa;\n            padding: 20px;\n            border-radius: 10px;\n            margin-bottom: 20px;\n            border: 1px solid #dee2e6;\n          }\n          .customer-info h2 {\n            color: #2c3e50;\n            margin: 0 0 15px 0;\n          }\n          .info-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 15px;\n          }\n          .info-item {\n            background: white;\n            padding: 15px;\n            border-radius: 8px;\n            border-left: 4px solid #3498db;\n          }\n          .info-item h4 {\n            margin: 0 0 8px 0;\n            color: #34495e;\n            font-size: 14px;\n          }\n          .info-item .value {\n            font-size: 18px;\n            font-weight: bold;\n            color: #2c3e50;\n          }\n          .transactions-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-top: 20px;\n          }\n          .transactions-table th, .transactions-table td {\n            border: 1px solid #ddd;\n            padding: 12px;\n            text-align: ${t?"right":"left"};\n          }\n          .transactions-table th {\n            background: #3498db;\n            color: white;\n            font-weight: bold;\n          }\n          .transactions-table tr:nth-child(even) {\n            background: #f9f9f9;\n          }\n          .cash {\n            color: #27ae60;\n            font-weight: bold;\n          }\n          .credit {\n            color: #e74c3c;\n            font-weight: bold;\n          }\n          .payment-row {\n            background-color: #f0f8ff !important;\n            border-left: 3px solid #28a745;\n          }\n          .sale-row {\n            background-color: #fff8f0 !important;\n            border-left: 3px solid #007bff;\n          }\n          .payment-amount {\n            color: #28a745;\n            font-weight: bold;\n          }\n          .sale-amount {\n            color: #007bff;\n            font-weight: bold;\n          }\n          .summary {\n            background: #ecf0f1;\n            padding: 20px;\n            border-radius: 10px;\n            margin-top: 20px;\n          }\n          .summary h3 {\n            color: #2c3e50;\n            margin: 0 0 15px 0;\n          }\n          .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n            gap: 15px;\n          }\n          .summary-item {\n            text-align: center;\n          }\n          .summary-item .label {\n            font-size: 14px;\n            color: #7f8c8d;\n          }\n          .summary-item .value {\n            font-size: 20px;\n            font-weight: bold;\n            color: #2c3e50;\n          }\n          .footer {\n            text-align: center;\n            margin-top: 30px;\n            color: #7f8c8d;\n            border-top: 1px solid #ddd;\n            padding-top: 20px;\n          }\n          @media print {\n            body { margin: 0; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📋 ${n("customerTransactionsReport","تقرير معاملات الزبون")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</p>\n          <p>${Et.storeName} - ${n("transactionsAnalysis","تحليل المعاملات")}</p>\n        </div>\n\n        <div class="customer-info">\n          <h2>👤 ${n("customerInformation","معلومات العميل")}</h2>\n          <div class="info-grid">\n            <div class="info-item">\n              <h4>${n("customerName","اسم العميل")}</h4>\n              <div class="value">${e.customer.name}</div>\n            </div>\n            <div class="info-item">\n              <h4>${n("phone","الهاتف")}</h4>\n              <div class="value">${e.customer.phone||n("notSpecified","غير محدد")}</div>\n            </div>\n            <div class="info-item">\n              <h4>${n("email","البريد الإلكتروني")}</h4>\n              <div class="value">${e.customer.email||n("notSpecified","غير محدد")}</div>\n            </div>\n            <div class="info-item">\n              <h4>${n("currentBalance","الرصيد الحالي")}</h4>\n              <div class="value ${e.customer.balance>0?"credit":"cash"}">${Gt(e.customer.balance||0)}</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="summary">\n          <h3>📊 ${n("transactionsSummary","ملخص المعاملات")}</h3>\n          <div class="summary-grid">\n            <div class="summary-item">\n              <div class="label">${n("totalTransactions","إجمالي المعاملات")}</div>\n              <div class="value">${e.totalOperations}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalSales","إجمالي المبيعات")}</div>\n              <div class="value">${Gt(e.totalAmount)}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalPayments","إجمالي المدفوعات")}</div>\n              <div class="value">${Gt(e.totalPayments)}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("cashTransactions","المعاملات النقدية")}</div>\n              <div class="value cash">${e.cashOperations.length}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("creditTransactions","معاملات الدين")}</div>\n              <div class="value credit">${e.creditOperations.length}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalCash","إجمالي النقدي")}</div>\n              <div class="value cash">${Gt(e.totalCash)}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalCredit","إجمالي الدين")}</div>\n              <div class="value credit">${Gt(e.totalCredit)}</div>\n            </div>\n          </div>\n        </div>\n\n        <table class="transactions-table">\n          <thead>\n            <tr>\n              <th>${n("operationType","نوع العملية")}</th>\n              <th>${n("invoiceNumber","رقم الفاتورة")}</th>\n              <th>${n("date","التاريخ")}</th>\n              <th>${n("paymentMethod","طريقة الدفع")}</th>\n              <th>${n("amount","المبلغ")}</th>\n              <th>${n("status","الحالة")}</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${e.allOperations.map((e=>`\n              <tr class="${"payment"===e.type?"payment-row":"sale-row"}">\n                <td>\n                  ${"payment"===e.type?"💰 "+n("payment","دفعة"):"🧾 "+n("sale","مبيعة")}\n                </td>\n                <td>${e.invoiceNumber}</td>\n                <td>${new Date(e.date).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</td>\n                <td class="${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                  ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}\n                </td>\n                <td class="${"payment"===e.type?"payment-amount":"sale-amount"}">\n                  ${"payment"===e.type?`-${Gt(Math.abs(e.finalTotal))}`:`+${Gt(e.finalTotal)}`}\n                </td>\n                <td class="${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                  ${"payment"===e.type?n("paymentReceived","دفعة مستلمة"):"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")}\n                </td>\n              </tr>\n            `)).join("")}\n          </tbody>\n        </table>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,r=window.open("","_blank","width=1200,height=800");r.document.write(s),r.document.close(),r.onload=()=>setTimeout((()=>r.print()),1e3),Jt(`🖨️ ${n("transactionsReportOpened","تم فتح تقرير المعاملات للطباعة")} - ${e.customer.name}`,"success",3e3)})(ie),children:["🖨️ ",n("printA4","طباعة A4")]}),t.jsxs("button",{className:"btn btn-warning",onClick:()=>(e=>{const t="ar"===N,a=`\n      <!DOCTYPE html>\n      <html dir="${t?"rtl":"ltr"}" lang="${N}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("customerTransactionsReport","تقرير معاملات الزبون")} - ${e.customer.name}</title>\n        <style>\n          @page {\n            size: 80mm auto;\n            margin: 0;\n          }\n\n          body {\n            font-family: 'Courier New', monospace;\n            direction: ${t?"rtl":"ltr"};\n            margin: 0;\n            padding: 3mm;\n            font-size: 14px;\n            font-weight: bold;\n            width: 74mm;\n            text-align: center;\n            color: black;\n            background: white;\n            line-height: 1.4;\n          }\n\n          .header {\n            text-align: center;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n            margin-bottom: 4mm;\n          }\n\n          .header h1 {\n            font-size: 16px;\n            font-weight: bold;\n            margin: 0 0 2mm 0;\n            text-transform: uppercase;\n          }\n\n          .header p {\n            font-size: 12px;\n            font-weight: bold;\n            margin: 1mm 0;\n          }\n\n          .customer-info {\n            margin-bottom: 4mm;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n            text-align: center;\n          }\n\n          .customer-info h2 {\n            font-size: 14px;\n            font-weight: bold;\n            margin: 0 0 2mm 0;\n            text-transform: uppercase;\n          }\n\n          .info-line {\n            font-size: 12px;\n            font-weight: bold;\n            margin: 2mm 0;\n            display: flex;\n            justify-content: space-between;\n          }\n\n          .summary {\n            margin-bottom: 4mm;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n          }\n\n          .summary h3 {\n            font-size: 14px;\n            font-weight: bold;\n            margin: 0 0 2mm 0;\n            text-transform: uppercase;\n            text-align: center;\n          }\n\n          .summary-line {\n            font-size: 12px;\n            font-weight: bold;\n            margin: 2mm 0;\n            display: flex;\n            justify-content: space-between;\n            padding: 1mm 0;\n            border-bottom: 1px dashed #666;\n          }\n\n          .transactions {\n            margin-bottom: 4mm;\n          }\n\n          .transactions h3 {\n            font-size: 14px;\n            font-weight: bold;\n            margin: 0 0 3mm 0;\n            text-transform: uppercase;\n            text-align: center;\n          }\n\n          .transaction-item {\n            font-size: 11px;\n            font-weight: bold;\n            margin: 3mm 0;\n            border: 1px solid black;\n            padding: 2mm;\n            background: #f8f8f8;\n          }\n\n          .transaction-header {\n            font-weight: bold;\n            font-size: 12px;\n            text-align: center;\n            margin-bottom: 1mm;\n          }\n\n          .transaction-details {\n            margin: 1mm 0;\n            display: flex;\n            justify-content: space-between;\n            font-size: 11px;\n          }\n\n          .payment-transaction {\n            background-color: #e8f5e8;\n            border: 2px solid #28a745;\n          }\n\n          .sale-transaction {\n            background-color: #e8f4fd;\n            border: 2px solid #007bff;\n          }\n\n          .payment-amount {\n            color: #28a745;\n            font-weight: bold;\n            font-size: 12px;\n          }\n\n          .sale-amount {\n            color: #007bff;\n            font-weight: bold;\n            font-size: 12px;\n          }\n\n          .footer {\n            text-align: center;\n            font-size: 10px;\n            font-weight: bold;\n            margin-top: 4mm;\n            border-top: 2px solid black;\n            padding-top: 3mm;\n          }\n\n          .developer-footer {\n            margin-top: 3mm;\n            padding-top: 2mm;\n            border-top: 1px dashed black;\n            font-size: 10px;\n            font-weight: bold;\n          }\n\n          .developer-name {\n            margin-bottom: 1mm;\n          }\n\n          .developer-phone {\n            font-size: 12px;\n            font-weight: bold;\n          }\n\n          @media print {\n            body {\n              width: 80mm !important;\n              font-size: 14px !important;\n              font-weight: bold !important;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📋 ${n("customerTransactionsReport","تقرير معاملات الزبون")}</h1>\n          <p>${(new Date).toLocaleDateString("ar"===N?"ar-DZ":"fr"===N?"fr-FR":"en-US")}</p>\n          <p>${Et.storeName}</p>\n        </div>\n\n        <div class="customer-info">\n          <h2>👤 ${n("customerInformation","معلومات العميل")}</h2>\n          <div class="info-line">\n            <span>${n("name","الاسم")}:</span>\n            <span>${e.customer.name}</span>\n          </div>\n          <div class="info-line">\n            <span>${n("phone","الهاتف")}:</span>\n            <span>${e.customer.phone||n("notSpecified","غير محدد")}</span>\n          </div>\n          <div class="info-line" style="background: #f0f0f0; padding: 2mm; border: 2px solid black; font-size: 14px;">\n            <span>${n("currentBalance","الرصيد")}:</span>\n            <span style="font-size: 16px;">${Gt(e.customer.balance||0)}</span>\n          </div>\n        </div>\n\n        <div class="summary">\n          <h3>📊 ${n("transactionsSummary","ملخص المعاملات")}</h3>\n          <div class="summary-line">\n            <span>${n("totalTransactions","إجمالي المعاملات")}:</span>\n            <span>${e.totalOperations}</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("totalSales","إجمالي المبيعات")}:</span>\n            <span>${Gt(e.totalAmount)}</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("totalPayments","إجمالي المدفوعات")}:</span>\n            <span>${Gt(e.totalPayments)}</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("cashTransactions","المعاملات النقدية")}:</span>\n            <span>${e.cashOperations.length} (${Gt(e.totalCash)})</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("creditTransactions","معاملات الدين")}:</span>\n            <span>${e.creditOperations.length} (${Gt(e.totalCredit)})</span>\n          </div>\n        </div>\n\n        <div class="transactions">\n          <h3>📋 ${n("allTransactionsList","قائمة جميع المعاملات")}</h3>\n          ${e.allOperations.map((e=>`\n            <div class="transaction-item ${"payment"===e.type?"payment-transaction":"sale-transaction"}">\n              <div class="transaction-header">\n                ${"payment"===e.type?"💰 "+n("payment","دفعة"):"🧾 "+n("sale","مبيعة")} - ${e.invoiceNumber}\n              </div>\n              <div class="transaction-details">\n                ${n("date","التاريخ")}: ${new Date(e.date).toLocaleDateString()}\n              </div>\n              <div class="transaction-details">\n                ${n("paymentMethod","طريقة الدفع")}: ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod}\n              </div>\n              <div class="transaction-details">\n                <span class="${"payment"===e.type?"payment-amount":"sale-amount"}">\n                  ${n("amount","المبلغ")}: ${"payment"===e.type?`-${Gt(Math.abs(e.finalTotal))}`:`+${Gt(e.finalTotal)}`}\n                </span>\n              </div>\n              ${e.time?`<div class="transaction-details">${n("time","الوقت")}: ${e.time}</div>`:""}\n            </div>\n          `)).join("")}\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي")}</p>\n\n          <div class="developer-footer">\n            <div class="developer-name">Developed by iCode DZ</div>\n            <div class="developer-phone">0551930589</div>\n          </div>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=500,height=700");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Jt(`🧾 ${n("thermalTransactionsReportOpened","تم فتح تقرير المعاملات للطباعة الحرارية")} - ${e.customer.name}`,"success",3e3)})(ie),children:["🧾 ",n("printThermal","طباعة حرارية")]}),t.jsxs("button",{className:"btn btn-primary",onClick:()=>re(!1),children:["✅ ",n("close","إغلاق")]})]})]})}),le&&de&&t.jsx("div",{className:"modal-overlay",onClick:()=>ce(!1),children:t.jsxs("div",{className:`modal-content confirmation-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["🗑️ ",n("confirmDeleteCustomer","هل أنت متأكد من حذف هذا الزبون؟")]}),t.jsx("button",{className:"modal-close",onClick:()=>ce(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"confirmation-content",children:[t.jsx("div",{className:"warning-icon",children:"⚠️"}),t.jsxs("div",{className:"confirmation-text",children:[t.jsx("p",{children:t.jsxs("strong",{children:[n("customerToDelete","الزبون المراد حذفه"),":"]})}),t.jsxs("div",{className:"customer-details",children:[t.jsxs("p",{children:[t.jsxs("strong",{children:[n("customerName","اسم الزبون"),":"]})," ",de.name]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("customerNumber","رقم الزبون"),":"]})," ",de.id]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("phone","الهاتف"),":"]})," ",de.phone||"-"]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("balance","الرصيد"),":"]}),t.jsxs("span",{className:de.balance<=0?"text-success":"text-danger",children:[de.balance.toLocaleString()," ",Et.currency]})]})]}),t.jsx("div",{className:"warning-message",children:t.jsxs("p",{children:["⚠️ ",n("deleteWarning","تحذير: هذا الإجراء لا يمكن التراجع عنه!")]})})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-danger",onClick:()=>{if(de){const e=fa.filter((e=>e.id!==de.id));ya(e),Jt(`🗑️ ${n("customerDeletedSuccessfully","تم حذف الزبون بنجاح")} - ${de.name}`,"success",3e3),ce(!1),me(null)}},children:["🗑️ ",n("yes","نعم")," - ",n("deleteCustomer","حذف الزبون")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:()=>ce(!1),children:["❌ ",n("no","لا")," - ",n("cancel","إلغاء")]})]})]})}),he&&pe&&t.jsx("div",{className:"modal-overlay",onClick:()=>ue(!1),children:t.jsxs("div",{className:`modal-content payment-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["💰 ",n("payCustomerDebt","تسديد فواتير الزبون")," - ",pe.name]}),t.jsx("button",{className:"modal-close",onClick:()=>ue(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"payment-content",children:[t.jsx("div",{className:"customer-payment-info",children:t.jsxs("div",{className:"payment-info-card",children:[t.jsxs("h3",{children:["👤 ",n("customerInfo","معلومات الزبون")]}),t.jsxs("div",{className:"payment-info-grid",children:[t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("customerName","اسم الزبون"),":"]})," ",pe.name]}),t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("customerNumber","رقم الزبون"),":"]})," ",pe.id]}),t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("phone","الهاتف"),":"]})," ",pe.phone||"-"]}),t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("currentBalance","الرصيد الحالي"),":"]}),t.jsx("span",{className:"debt-amount",children:Gt(pe.balance)})]})]})]})}),t.jsxs("div",{className:"payment-form",children:[t.jsxs("h3",{children:["💳 ",n("paymentDetails","تفاصيل الدفع")]}),t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("paymentAmount","مبلغ الدفع")," *"]}),t.jsx("input",{type:"number",value:ge,onChange:e=>ve(parseFloat(e.target.value)||0),placeholder:"0.00",min:"0",max:pe.balance,step:"0.01",required:!0,autoFocus:!0}),t.jsxs("small",{className:"payment-help",children:["💡 ",n("maxPaymentAmount","الحد الأقصى"),": ",Gt(pe.balance)]})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("paymentMethod","طريقة الدفع")," *"]}),t.jsxs("select",{value:be,onChange:e=>fe(e.target.value),required:!0,children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"بطاقة",children:n("creditCard","بطاقة ائتمان")}),t.jsx("option",{value:"تحويل",children:n("bankTransfer","تحويل بنكي")}),t.jsx("option",{value:"شيك",children:n("check","شيك")})]})]})]}),t.jsxs("div",{className:"payment-summary",children:[t.jsxs("div",{className:"summary-row",children:[t.jsxs("span",{children:[n("currentDebt","الدين الحالي"),":"]}),t.jsx("span",{className:"debt-amount",children:Gt(pe.balance)})]}),t.jsxs("div",{className:"summary-row",children:[t.jsxs("span",{children:[n("paymentAmount","مبلغ الدفع"),":"]}),t.jsxs("span",{className:"payment-amount",children:["-",Gt(ge)]})]}),t.jsx("div",{className:"summary-divider"}),t.jsxs("div",{className:"summary-row total",children:[t.jsxs("span",{children:[n("remainingBalance","الرصيد المتبقي"),":"]}),t.jsx("span",{className:pe.balance-ge<=0?"text-success":"text-danger",children:Gt(pe.balance-ge)})]})]})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!pe||ge<=0)return void Jt(n("pleaseEnterValidAmount","يرجى إدخال مبلغ صحيح"),"error");if(ge>pe.balance)return void Jt(n("paymentExceedsBalance","المبلغ المدخل أكبر من رصيد الزبون"),"error");const e=fa.map((e=>e.id===pe.id?{...e,balance:e.balance-ge}:e));ya(e);const t={id:"PAY-"+Date.now(),customerId:pe.id,customerName:pe.name,amount:ge,paymentMethod:be,date:(new Date).toISOString().split("T")[0],time:(new Date).toLocaleTimeString(),type:"payment"},a=[...JSON.parse(localStorage.getItem("icaldz-payments")||"[]"),t];localStorage.setItem("icaldz-payments",JSON.stringify(a)),Jt(`💰 ${n("paymentProcessedSuccessfully","تم تسديد المبلغ بنجاح")} - ${Gt(ge)} ${n("for","لـ")} ${pe.name}`,"success",4e3),La()},children:["💰 ",n("processPayment","تأكيد الدفع")," - ",Gt(ge)]}),t.jsxs("button",{className:"btn btn-secondary",onClick:La,children:["❌ ",n("cancel","إلغاء")]})]})]})}),rt&&t.jsx("div",{className:"modal-overlay",children:t.jsxs("div",{className:`modal lang-${N}`,children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("h3",{children:ot.id?n("editExpense","تعديل مصروف"):n("addNewExpense","إضافة مصروف جديد")}),t.jsx("button",{className:"close-button",onClick:()=>it(!1),children:"×"})]}),t.jsxs("div",{className:"modal-content",children:[t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:ot.date,onChange:e=>lt({...ot,date:e.target.value}),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("category","الفئة")}),t.jsxs("select",{value:ot.category,onChange:e=>lt({...ot,category:e.target.value}),required:!0,children:[t.jsx("option",{value:"رواتب",children:n("salariesWages","رواتب وأجور")}),t.jsx("option",{value:"إيجار",children:n("rent","إيجار")}),t.jsx("option",{value:"مرافق",children:n("utilities","مرافق (كهرباء، ماء، إنترنت)")}),t.jsx("option",{value:"ضرائب",children:n("taxesFees","ضرائب ورسوم")}),t.jsx("option",{value:"تسويق",children:n("marketingAdvertising","تسويق وإعلان")}),t.jsx("option",{value:"صيانة",children:n("maintenanceRepairs","صيانة وإصلاحات")}),t.jsx("option",{value:"نقل",children:n("transportationTravel","نقل ومواصلات")}),t.jsx("option",{value:"أخرى",children:n("otherExpenses","مصاريف أخرى")})]})]})]}),t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("amount","المبلغ")}),t.jsx("input",{type:"number",value:ot.amount,onChange:e=>lt({...ot,amount:e.target.value}),required:!0,min:"0"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentMethod","طريقة الدفع")}),t.jsxs("select",{value:ot.paymentMethod,onChange:e=>lt({...ot,paymentMethod:e.target.value}),children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"بطاقة",children:n("creditCard","بطاقة ائتمان")}),t.jsx("option",{value:"تحويل",children:n("bankTransfer","تحويل بنكي")}),t.jsx("option",{value:"شيك",children:n("check","شيك")}),t.jsx("option",{value:"أخرى",children:n("otherPaymentMethod","طريقة أخرى")})]})]})]}),t.jsx("div",{className:"form-row",children:t.jsxs("div",{className:"form-group full-width",children:[t.jsx("label",{children:n("description","الوصف")}),t.jsx("textarea",{value:ot.description,onChange:e=>lt({...ot,description:e.target.value}),placeholder:n("expenseDescription","أدخل وصفاً تفصيلياً للمصروف"),rows:"3"})]})})]}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsx("button",{className:"cancel-button",onClick:()=>it(!1),children:n("cancel","إلغاء")}),t.jsx("button",{className:"save-button",onClick:()=>{if(ot.date&&ot.category&&ot.amount){if(ot.id){const e=at.map((e=>e.id===ot.id?ot:e));nt(e),Jt(`✅ ${n("expenseUpdatedSuccess","تم تحديث المصروف بنجاح")}`,"success")}else{const e={...ot,id:Date.now().toString(),amount:parseFloat(ot.amount)};nt([...at,e]),Jt(`✅ ${n("expenseAddedSuccess","تم إضافة المصروف بنجاح")}`,"success")}lt({id:"",date:(new Date).toISOString().split("T")[0],category:"رواتب",amount:0,description:"",paymentMethod:"نقداً"}),it(!1)}else Jt(`❌ ${n("fillRequiredFields","يرجى ملء جميع الحقول المطلوبة")}`,"error")},children:n("save","حفظ")})]})]})}),ct&&t.jsx("div",{className:"modal-overlay",onClick:ln,children:t.jsxs("div",{className:"modal-content supplier-modal "+("ar"!==N?"modal-ltr":""),onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["🏭 ",n("addNewSupplier","إضافة مورّد جديد")]}),t.jsx("button",{className:"modal-close",onClick:ln,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierNumber","رقم المورد")}),t.jsx("input",{type:"text",value:vt.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("supplierName","اسم المورد")," *"]}),t.jsx("input",{type:"text",value:vt.name,onChange:e=>bt({...vt,name:e.target.value}),placeholder:n("enterSupplierName","أدخل اسم المورد"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierPhone","رقم الهاتف")}),t.jsx("input",{type:"tel",value:vt.phone,onChange:e=>bt({...vt,phone:e.target.value}),placeholder:n("phoneNumberPlaceholder","رقم الهاتف")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierEmail","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:vt.email,onChange:e=>bt({...vt,email:e.target.value}),placeholder:n("emailPlaceholder","البريد الإلكتروني")})]}),t.jsxs("div",{className:"form-group full-width",children:[t.jsx("label",{children:n("supplierAddress","العنوان")}),t.jsx("input",{type:"text",value:vt.address,onChange:e=>bt({...vt,address:e.target.value}),placeholder:n("fullAddress","العنوان الكامل")})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!vt.name.trim())return void Jt(n("pleaseEnterSupplierName","يرجى إدخال اسم المورد"),"error");const e=vt.name.trim();if(Qe.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==vt.id)))return void Jt(`❌ ${n("supplierNameAlreadyExists","اسم المورد موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);if(vt.phone&&""!==vt.phone.trim()){const e=Qe.find((e=>e.phone===vt.phone.trim()&&e.id!==vt.id));if(e)return void Jt(`❌ ${n("phoneNumberAlreadyExists","رقم الهاتف موجود بالفعل")}: "${vt.phone}" - ${n("usedBySupplier","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const t={...vt,createdAt:(new Date).toISOString()},a=[...Qe,t];an(a),ln(),Jt(n("supplierAddedSuccessfully","تم إضافة المورد بنجاح"),"success")},children:["✅ ",n("saveSupplier","حفظ المورد")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:ln,children:["❌ ",n("cancel","إلغاء")]})]})]})}),ut&&xt&&t.jsx("div",{className:"modal-overlay",onClick:dn,children:t.jsxs("div",{className:"modal-content supplier-modal "+("ar"!==N?"modal-ltr":""),onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["✏️ ",n("editSupplier","تعديل المورد")]}),t.jsx("button",{className:"modal-close",onClick:dn,children:"×"})]}),t.jsxs("div",{className:"modal-body",children:[t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierNumber","رقم المورد")}),t.jsx("input",{type:"text",value:xt.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("supplierName","اسم المورد")," *"]}),t.jsx("input",{type:"text",value:xt.name,onChange:e=>gt({...xt,name:e.target.value}),placeholder:n("enterSupplierName","أدخل اسم المورد"),autoFocus:!0})]})]}),t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierPhone","رقم الهاتف")}),t.jsx("input",{type:"tel",value:xt.phone,onChange:e=>gt({...xt,phone:e.target.value}),placeholder:n("enterSupplierPhone","أدخل رقم الهاتف")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierEmail","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:xt.email,onChange:e=>gt({...xt,email:e.target.value}),placeholder:n("enterSupplierEmail","أدخل البريد الإلكتروني")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierAddress","العنوان")}),t.jsx("textarea",{value:xt.address,onChange:e=>gt({...xt,address:e.target.value}),placeholder:n("enterSupplierAddress","أدخل العنوان"),rows:"3"})]})]}),t.jsxs("div",{className:"modal-footer",children:[t.jsx("button",{className:"btn btn-secondary",onClick:dn,children:n("cancel","إلغاء")}),t.jsx("button",{className:"btn btn-primary",onClick:()=>{if(!xt.name.trim())return void Jt(n("pleaseEnterSupplierName","يرجى إدخال اسم المورد"),"error");const e=Qe.map((e=>e.id===xt.id?xt:e));an(e),dn(),Jt(n("supplierUpdatedSuccessfully","تم تحديث المورد بنجاح"),"success")},children:n("save","حفظ")})]})]})}),mt&&t.jsx("div",{className:"modal-overlay",onClick:()=>ht(!1),children:t.jsxs("div",{className:`modal-content large-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("h2",{children:n("suppliersManagement","إدارة الموردين")}),t.jsx("button",{className:"modal-close",onClick:()=>ht(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"suppliers-management",children:[t.jsx("div",{className:"suppliers-header",children:t.jsxs("button",{className:"btn btn-success",onClick:on,children:["➕ ",n("addNewSupplierButton","إضافة مورد جديد")]})}),t.jsx("div",{className:"suppliers-table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==N?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("supplierID","رقم المورد")}),t.jsx("th",{children:n("supplierNameHeader","اسم المورد")}),t.jsx("th",{children:n("supplierPhoneHeader","رقم الهاتف")}),t.jsx("th",{children:n("supplierEmailHeader","البريد الإلكتروني")}),t.jsx("th",{children:n("supplierAddressHeader","العنوان")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Qe.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:n("noSuppliersAdded","لا توجد موردين مضافين")})}):Qe.map((e=>t.jsxs("tr",{children:[t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:e.email}),t.jsx("td",{children:e.address}),t.jsx("td",{children:t.jsxs("button",{className:"btn btn-danger btn-sm",onClick:()=>cn(e.id),children:["🗑️ ",n("deleteSupplier","حذف")]})})]},e.id)))})]})})]})}),t.jsx("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:t.jsx("button",{className:"btn btn-secondary",onClick:()=>ht(!1),children:n("close","إغلاق")})})]})}),aa&&t.jsx("div",{className:"modal-overlay",onClick:us,children:t.jsxs("div",{className:`modal-content product-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("h2",{children:Mn.find((e=>e.barcode===Rn.barcode&&""!==Rn.barcode.trim()))?`✏️ ${n("editExistingProduct","تعديل منتج موجود")}`:`📦 ${n("addNewProduct","إضافة منتج جديد")}`}),t.jsx("button",{className:"modal-close",onClick:us,children:"×"})]}),t.jsxs("div",{className:"modal-body",children:[!Mn.find((e=>e.id===Rn.id))&&t.jsxs("div",{className:"barcode-scanner-section",children:[t.jsxs("h3",{children:["📷 ",n("scanBarcode","مسح الباركود")]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanBarcodeOrEnter","امسح الباركود أو أدخله يدوياً - سيتم التوليد التلقائي إذا تُرك فارغاً"),value:Rn.barcode,onChange:e=>{(e=>{if(Mn.find((e=>e.id===Rn.id)))return On({...Rn,barcode:e}),void console.log("📦 Product: Editing existing product barcode:",e);const t=$(e);if(console.log("📦 Product: Enhanced barcode input:",{raw:e,cleaned:t,scannerTool:"Enhanced for barcode tools - NO AUTO-SAVE"}),On({...Rn,barcode:t}),t.length>=3){const e=Mn.find((e=>e.barcode===t&&""!==t.trim()));e?Jt(`ℹ️ ${n("productExistsWithBarcode","يوجد منتج بهذا الباركود")}: ${e.name}. ${n("checkBeforeSaving","تحقق قبل الحفظ")}`,"warning",4e3):t.length>=8&&/^\d+$/.test(t)?Jt(`✅ ${n("barcodeScannedSuccess","تم مسح الباركود بنجاح - باركود جديد")}: ${t}`,"success",2e3):Jt(`✅ ${n("barcodeAvailable","الباركود متاح للاستخدام - منتج جديد")}: ${t}`,"success",2e3)}})(e.target.value)},onKeyDown:e=>{if("Enter"===e.key){const t=e.target.value;t.length>=3&&(e=>{if(Mn.find((e=>e.id===Rn.id)))return void Jt(`📋 ${n("currentBarcode","الباركود الحالي")}: ${e}`,"info",2e3);const t=Mn.find((t=>t.barcode===e));t?Jt(`⚠️ ${n("productExistsWithBarcode","يوجد منتج بهذا الباركود")}: ${t.name}. ${n("checkBeforeSaving","تحقق قبل الحفظ")}`,"warning",4e3):Jt(`✅ ${n("barcodeAvailable","الباركود متاح للاستخدام - منتج جديد")}`,"success",2e3)})(t)}},onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1),console.log("🔧 BARCODE FIX: Product barcode focused - shortcuts disabled"))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Product barcode blurred - shortcuts re-enabled"))}),100)},className:"barcode-input",autoFocus:!0})]}),t.jsxs("div",{className:"barcode-actions",children:[t.jsxs("button",{type:"button",className:"btn btn-secondary btn-sm",onClick:()=>{const e=ms();On({...Rn,barcode:e}),Jt(`🔢 ${n("autoBarcodeGenerated","تم توليد باركود تلقائي")}: ${e}`,"info",3e3)},children:["🔢 ",n("generateAutoBarcode","توليد باركود تلقائي")]}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{On({...Rn,barcode:""}),Jt(`🗑️ ${n("barcodeCleared","تم مسح الباركود")}`,"info",2e3)},children:["🗑️ ",n("clearBarcode","مسح الباركود")]})]}),t.jsxs("small",{className:"barcode-help",children:["💡 ",n("barcodeHelp","يمكنك مسح الباركود باستخدام قارئ الباركود أو إدخاله يدوياً. إذا كان الباركود موجود في المخزون، سيتم عرض معلومات المنتج للتعديل. إذا تُرك فارغاً، سيتم توليد باركود تلقائياً عند الحفظ.")]})]}),t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("productNumber","رقم المنتج")}),t.jsx("input",{type:"text",value:Rn.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("productName","اسم المنتج")," *"]}),t.jsx("input",{type:"text",value:Rn.name,onChange:e=>{const t=e.target.value;if(On({...Rn,name:t}),t.trim().length>2){Mn.find((e=>e.name.toLowerCase().trim()===t.toLowerCase().trim()&&e.id!==Rn.id))?(e.target.style.borderColor="#e74c3c",e.target.style.backgroundColor="#fdf2f2"):(e.target.style.borderColor="#27ae60",e.target.style.backgroundColor="#f0fff4")}else e.target.style.borderColor="#ddd",e.target.style.backgroundColor="white"},placeholder:n("enterProductName","أدخل اسم المنتج"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("category","الفئة")," *"]}),t.jsxs("div",{className:"category-input-group",children:[t.jsxs("select",{value:Rn.category,onChange:e=>On({...Rn,category:e.target.value}),required:!0,children:[t.jsx("option",{value:"",children:n("selectCategory","اختر الفئة")}),Fn.map((e=>t.jsx("option",{value:e,children:e},e)))]}),t.jsx("button",{type:"button",className:"btn btn-secondary btn-sm",onClick:kn,title:n("manageCategories","إدارة الفئات"),children:"⚙️"})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["📷 ",n("currentBarcode","الباركود الحالي")]}),t.jsxs("div",{className:"barcode-input-group",children:[t.jsx("input",{type:"text",value:Rn.barcode,onChange:e=>{const t=e.target.value;if((e=>{On({...Rn,barcode:e})})(t),t.trim().length>2){Mn.find((e=>e.barcode===t&&e.id!==Rn.id))?(e.target.style.borderColor="#e74c3c",e.target.style.backgroundColor="#fdf2f2"):(e.target.style.borderColor="#27ae60",e.target.style.backgroundColor="#f0fff4")}else e.target.style.borderColor="#ddd",e.target.style.backgroundColor="white"},placeholder:n("barcodeWillShow","سيتم عرض الباركود هنا"),className:"barcode-input-field"}),t.jsx("button",{type:"button",className:"btn btn-secondary btn-sm",onClick:()=>{const e=ms();On({...Rn,barcode:e}),Jt(`🔢 ${n("autoBarcodeGenerated","تم توليد باركود تلقائي")}: ${e}`,"info",3e3)},title:n("generateAutoBarcode","توليد باركود تلقائي"),children:"🔢"}),t.jsx("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{On({...Rn,barcode:""}),Jt(`🗑️ ${n("barcodeCleared","تم مسح الباركود")}`,"info",2e3)},title:n("clearBarcode","مسح الباركود"),children:"🗑️"})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("buyPrice","سعر الشراء")," *"]}),t.jsx("input",{type:"number",value:Rn.buyPrice,onChange:e=>On({...Rn,buyPrice:parseFloat(e.target.value)||0}),placeholder:"0.00",min:"0",step:"0.01",required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("sellPrice","سعر البيع")," *"]}),t.jsx("input",{type:"number",value:Rn.sellPrice,onChange:e=>On({...Rn,sellPrice:parseFloat(e.target.value)||0}),placeholder:"0.00",min:"0",step:"0.01",required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("currentQuantity","الكمية الحالية")}),t.jsx("input",{type:"number",value:Rn.stock,onChange:e=>On({...Rn,stock:parseInt(e.target.value)||0}),placeholder:"0",min:"0"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("minStock","الحد الأدنى")}),t.jsx("input",{type:"number",value:Rn.minStock,onChange:e=>On({...Rn,minStock:parseInt(e.target.value)||0}),placeholder:"5",min:"0"})]})]})]}),t.jsxs("div",{className:"modal-footer",children:[t.jsx("button",{className:"btn btn-success",onClick:ps,children:Mn.find((e=>e.barcode===Rn.barcode&&""!==Rn.barcode.trim()))?`✅ ${n("saveChanges","حفظ التعديلات")}`:`✅ ${n("saveProduct","حفظ المنتج")}`}),t.jsxs("button",{className:"btn btn-secondary",onClick:us,children:["❌ ",n("cancel","إلغاء")]})]})]})}),zn&&t.jsx("div",{className:"modal-overlay",onClick:Cn,children:t.jsxs("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header",children:[t.jsx("h2",{children:n("categoryManagement","إدارة فئات المنتجات")}),t.jsx("button",{className:"modal-close",onClick:Cn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"category-management",children:[t.jsxs("div",{className:"add-category-section",children:[t.jsx("h3",{children:n("addNewCategory","إضافة فئة جديدة")}),t.jsxs("div",{className:"form-row",children:[t.jsx("input",{type:"text",value:Un,onChange:e=>qn(e.target.value),placeholder:n("newCategoryName","اسم الفئة الجديدة"),onKeyPress:e=>"Enter"===e.key&&Dn()}),t.jsxs("button",{className:"btn btn-success",onClick:Dn,children:["➕ ",n("add","إضافة")]})]})]}),t.jsxs("div",{className:"categories-list",children:[t.jsx("h3",{children:n("existingCategories","الفئات الموجودة")}),t.jsx("div",{className:"categories-grid",children:Fn.map(((e,a)=>t.jsx("div",{className:"category-item",children:Gn===e?t.jsx("div",{className:"edit-category",children:t.jsx("input",{type:"text",defaultValue:e,onKeyPress:t=>{"Enter"===t.key&&In(e,t.target.value)},onBlur:t=>In(e,t.target.value),autoFocus:!0})}):t.jsxs("div",{className:"category-display",children:[t.jsx("span",{className:"category-name",children:e}),t.jsxs("div",{className:"category-actions",children:[t.jsx("button",{className:"btn btn-sm btn-info",onClick:()=>Vn(e),title:n("edit","تعديل"),children:"✏️"}),t.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>(e=>{const t=Mn.filter((t=>t.category===e));if(t.length>0){if(!window.confirm(`هذه الفئة مستخدمة في ${t.length} منتج. هل تريد حذفها؟ سيتم تحويل المنتجات إلى فئة "غير مصنف"`))return;const n=Mn.map((t=>t.category===e?{...t,category:"غير مصنف"}:t));En(n);const a=Fn.filter((t=>t!==e));a.includes("غير مصنف")||a.push("غير مصنف"),Sn(a)}else{const t=Fn.filter((t=>t!==e));Sn(t)}Jt(`✅ تم حذف الفئة "${e}" بنجاح`,"success")})(e),title:n("delete","حذف"),children:"🗑️"})]})]})},a)))})]})]})}),t.jsx("div",{className:"modal-footer",children:t.jsx("button",{className:"btn btn-secondary",onClick:Cn,children:"إغلاق"})})]})}),Zn&&_n&&t.jsx("div",{className:"modal-overlay",onClick:gn,children:t.jsxs("div",{className:`modal-content large-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsxs("h2",{children:[n("editInvoiceTitle","تعديل الفاتورة")," ",_n.invoiceNumber]}),t.jsx("button",{className:"modal-close",onClick:gn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"invoice-edit-form",children:[t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("input",{type:"text",value:_n.invoiceNumber,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:_n.date,onChange:e=>Hn({..._n,date:e.target.value})})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("customer","العميل")}),t.jsxs("select",{value:_n.customerId||"GUEST",onChange:e=>{const t=e.target.value,a=fa.find((e=>e.id===t));Hn({..._n,customerId:t,customerName:"GUEST"===t?n("walkInCustomer","زبون عابر"):a?.name||""})},children:[t.jsx("option",{value:"GUEST",children:n("walkInCustomer","زبون عابر")}),fa.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",e.phone]},e.id)))]})]})]}),t.jsx("div",{className:"sales-barcode-scanner-row "+("ar"!==N?"edit-invoice-layout-ltr":""),children:"ar"!==N?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"lcd-display-row lcd-left",children:t.jsx("div",{className:"lcd-screen-big",children:t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Gt(_n.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"})]})})}),t.jsxs("div",{className:"scanner-input-row scanner-right",children:[t.jsxs("h3",{className:"scanner-title",children:["📷 ",n("activeBarcodeScanner","Actif - Scanner le code-barres")," 📷"]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanToAddProduct","امسح الباركود لإضافة منتج"),value:Ja,onChange:ls,onKeyDown:cs,onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1),console.log("🔧 BARCODE FIX: Edit scanner focused - shortcuts disabled"))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Edit scanner blurred - shortcuts re-enabled"))}),100)},className:"barcode-input",ref:Ya,autoFocus:!0}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{_a("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]})]})]})]}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"scanner-input-row",children:[t.jsxs("h3",{className:"scanner-title",children:["📷 ",n("activeBarcodeScanner","نشط - مسح الباركود")," 📷"]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanToAddProduct","امسح الباركود لإضافة منتج"),value:Ja,onChange:ls,onKeyDown:cs,onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1),console.log("🔧 BARCODE FIX: Edit scanner (Arabic) focused - shortcuts disabled"))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Edit scanner (Arabic) blurred - shortcuts re-enabled"))}),100)},className:"barcode-input",ref:Ya,autoFocus:!0}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{_a("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]})]})]}),t.jsx("div",{className:"lcd-display-row",children:t.jsx("div",{className:"lcd-screen-big",children:t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Gt(_n.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"})]})})})]})}),t.jsxs("div",{className:"product-selection-section",children:[t.jsxs("h3",{children:["🛒 ",n("addProductFromList","إضافة منتج من القائمة")]}),t.jsx("div",{className:"product-add-form",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("selectProduct","اختر المنتج")}),t.jsxs("select",{value:ca,onChange:e=>da(e.target.value),children:[t.jsx("option",{value:"",children:n("selectProductOption","اختر منتج")}),Mn.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",Gt(e.sellPrice)," (",n("available","متوفر"),": ",e.stock,")"]},e.id)))]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("quantity","الكمية")}),t.jsx("input",{type:"number",value:ma,onChange:e=>ha(parseInt(e.target.value)||1),min:"1",placeholder:"1"})]}),t.jsx("div",{className:"form-group",children:t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!ca)return void Jt(`⚠️ ${n("pleaseSelectProduct","يرجى اختيار منتج")}`,"warning",3e3);const e=Mn.find((e=>e.id===ca));if(!e)return void Jt(`❌ ${n("productNotFound","لم يتم العثور على المنتج")}`,"error",3e3);if(e.stock<ma)return void Jt(`❌ ${n("insufficientStock","كمية غير كافية في المخزون")}: ${e.name} (${n("available","متوفر")}: ${e.stock})`,"error",3e3);const t=_n.items.findIndex((t=>t.productId===e.id));if(-1!==t){const a=[..._n.items],s=a[t].quantity+ma;if(s>e.stock)return void Jt(`❌ ${n("insufficientStock","كمية غير كافية في المخزون")}: ${e.name} (${n("available","متوفر")}: ${e.stock})`,"error",3e3);a[t]={...a[t],quantity:s,total:s*a[t].price};const r=a.reduce(((e,t)=>e+t.total),0),i=r*(Et.taxRate/100),o=r+i-_n.discount;Hn({..._n,items:a,total:r,tax:i,finalTotal:o}),Jt(`✅ ${n("quantityUpdated","تم تحديث الكمية")}: ${e.name} (${s})`,"success",2e3)}else{const t={id:Date.now(),productId:e.id,productName:e.name,name:e.name,price:e.sellPrice,quantity:ma,total:e.sellPrice*ma},a=[..._n.items,t],s=a.reduce(((e,t)=>e+t.total),0),r=s*(Et.taxRate/100),i=s+r-_n.discount;Hn({..._n,items:a,total:s,tax:r,finalTotal:i}),Jt(`✅ ${n("productAdded","تم إضافة المنتج")}: ${e.name} (${n("quantity","الكمية")}: ${ma})`,"success",2e3)}da(""),ha(1),b.play("addProduct")},disabled:!ca,children:["➕ ",n("addProduct","إضافة المنتج")]})})]})})]}),t.jsxs("div",{className:"invoice-items-section",children:[t.jsx("h3",{children:n("invoiceItems","عناصر الفاتورة")}),t.jsx("div",{className:"items-table",children:t.jsxs("table",{className:"data-table "+("ar"!==N?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("product","المنتج")}),t.jsx("th",{children:n("price","السعر")}),t.jsx("th",{children:n("quantity","الكمية")}),t.jsx("th",{children:n("total","المجموع")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:_n.items.map(((e,n)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:Gt(e.price)}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.quantity,onChange:e=>((e,t)=>{if(t<=0)return;const n=[..._n.items];n[e]={...n[e],quantity:t,total:n[e].price*t};const a=n.reduce(((e,t)=>e+t.total),0),s=a*(Et.taxRate/100),r=a+s-_n.discount;Hn({..._n,items:n,total:a,tax:s,finalTotal:r})})(n,parseInt(e.target.value)||1),min:"1",style:{width:"80px"}})}),t.jsx("td",{children:Gt(e.total)}),t.jsx("td",{children:t.jsx("button",{className:"btn btn-danger btn-sm",onClick:()=>(e=>{const t=_n.items.filter(((t,n)=>n!==e)),n=t.reduce(((e,t)=>e+t.total),0),a=n*(Et.taxRate/100),s=n+a-_n.discount;Hn({..._n,items:t,total:n,tax:a,finalTotal:s})})(n),children:"🗑️"})})]},n)))})]})})]}),t.jsxs("div",{className:"invoice-totals",children:[t.jsxs("div",{className:"totals-row",children:[t.jsx("span",{children:n("subtotalLabel","المجموع الفرعي:")}),t.jsx("span",{children:Gt(_n.total)})]}),t.jsxs("div",{className:"totals-row",children:[t.jsxs("span",{children:[n("taxLabel","الضريبة")," (",Et.taxRate,"%):"]}),t.jsx("span",{children:Gt(_n.tax)})]}),t.jsxs("div",{className:"totals-row total-final",children:[t.jsx("span",{children:n("finalTotalLabel","المجموع النهائي:")}),t.jsx("span",{children:Gt(_n.finalTotal)})]})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!_n)return;const e={};Wn.forEach((t=>{e[t.productId]||(e[t.productId]=0),e[t.productId]+=t.quantity})),_n.items.forEach((t=>{e[t.productId]||(e[t.productId]=0),e[t.productId]-=t.quantity}));const t=Mn.map((t=>{if(e[t.id]){const a=t.stock+e[t.id];return a<0?(Jt(`❌ ${n("insufficientStockForProduct","المخزون غير كافي للمنتج")} ${t.name}`,"error"),t):{...t,stock:a}}return t})),a=Ie.map((e=>e.invoiceNumber===_n.invoiceNumber?_n:e));En(t),Me(a),localStorage.setItem("icaldz-invoices",JSON.stringify(a)),Jt(`✅ ${n("invoiceUpdatedAndStockAdjusted","تم تحديث الفاتورة")} ${_n.invoiceNumber} ${n("andStockAdjusted","وتحديث المخزون")}`,"success"),gn()},children:["✅ ",n("saveChanges","حفظ التعديلات")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:gn,children:["❌ ",n("cancel","إلغاء")]})]})]})}),Kn&&Xn&&t.jsx("div",{className:"modal-overlay",onClick:bn,children:t.jsxs("div",{className:"modal-content large-modal",onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header",children:[t.jsxs("h2",{children:[n("returnProducts","إرجاع منتجات من الفاتورة")," ",Xn.invoiceNumber]}),t.jsx("button",{className:"modal-close",onClick:bn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"return-form",children:[t.jsxs("div",{className:"return-info",children:[t.jsxs("p",{children:[t.jsxs("strong",{children:[n("invoiceDate","تاريخ الفاتورة"),":"]})," ",new Date(Xn.date).toLocaleDateString("ar-DZ")]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("customer","العميل"),":"]})," ",Xn.customerName]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("originalTotal","المجموع الأصلي"),":"]})," ",Gt(Xn.finalTotal)]})]}),t.jsxs("div",{className:"return-items-section",children:[t.jsx("h3",{children:n("selectProductsToReturn","تحديد المنتجات المراد إرجاعها")}),t.jsx("div",{className:"items-table",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("product","المنتج")}),t.jsx("th",{children:n("price","السعر")}),t.jsx("th",{children:n("originalQuantity","الكمية الأصلية")}),t.jsx("th",{children:n("returnQuantity","كمية الإرجاع")}),t.jsx("th",{children:n("returnValue","قيمة الإرجاع")})]})}),t.jsx("tbody",{children:ta.map(((e,n)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:Gt(e.price)}),t.jsx("td",{children:e.maxReturnQuantity}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.returnQuantity,onChange:e=>((e,t)=>{const n=[...ta],a=n[e].maxReturnQuantity;t<0&&(t=0),t>a&&(t=a),n[e].returnQuantity=t,na(n)})(n,parseInt(e.target.value)||0),min:"0",max:e.maxReturnQuantity,style:{width:"80px"}})}),t.jsx("td",{children:Gt(e.price*e.returnQuantity)})]},n)))})]})})]}),t.jsx("div",{className:"return-summary",children:t.jsxs("div",{className:"summary-row",children:[t.jsxs("span",{children:[n("totalReturnValue","إجمالي قيمة الإرجاع"),":"]}),t.jsx("span",{children:Gt(ta.reduce(((e,t)=>e+t.price*t.returnQuantity),0))})]})})]})}),t.jsxs("div",{className:"modal-footer",children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{const e=ta.filter((e=>e.returnQuantity>0));if(0===e.length)return void Jt(`⚠️ ${n("pleaseSelectAtLeastOneProduct","يرجى تحديد كمية الإرجاع لمنتج واحد على الأقل")}`,"warning");const t=Mn.map((t=>{const n=e.find((e=>e.productId===t.id));return n?{...t,stock:t.stock+n.returnQuantity}:t})),a=Xn.items.map((t=>{const n=e.find((e=>e.productId===t.productId));if(n){const e=t.quantity-n.returnQuantity;return{...t,quantity:e,total:t.price*e}}return t})).filter((e=>e.quantity>0)),s=a.reduce(((e,t)=>e+t.total),0),r=s*(Et.taxRate/100),i=s+r-Xn.discount,o={...Xn,items:a,total:s,tax:r,finalTotal:i},l=Ie.map((e=>e.invoiceNumber===Xn.invoiceNumber?o:e)),c=[{id:"RET-"+Date.now(),originalInvoiceNumber:Xn.invoiceNumber,date:(new Date).toISOString().split("T")[0],items:e.map((e=>({productId:e.productId,productName:e.productName,quantity:e.returnQuantity,price:e.price,total:e.price*e.returnQuantity}))),totalAmount:e.reduce(((e,t)=>e+t.price*t.returnQuantity),0),processedBy:zt.name},...JSON.parse(localStorage.getItem("icaldz-returns")||"[]")];localStorage.setItem("icaldz-returns",JSON.stringify(c)),En(t),Me(l),localStorage.setItem("icaldz-invoices",JSON.stringify(l));const d=e.map((e=>`${e.productName}: ${e.returnQuantity}`)).join(", ");Jt(`✅ ${n("productsReturnedSuccessfully","تم إرجاع المنتجات")}: ${d} ${n("andStockUpdated","وتحديث المخزون")}`,"success",5e3),bn()},children:["✅ ",n("confirmReturn","تأكيد الإرجاع")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:bn,children:["❌ ",n("cancel","إلغاء")]})]})]})}),Ft&&t.jsx("div",{className:"modal-overlay",onClick:$n,children:t.jsxs("div",{className:`modal-content lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsx("h2",{children:n("addNewSellerTitle","إضافة بائع جديد")}),t.jsx("button",{className:"modal-close",onClick:$n,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("sellerID","رقم البائع")}),t.jsx("input",{type:"text",value:Ut.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("sellerNameLabel","اسم البائع")," *"]}),t.jsx("input",{type:"text",value:Ut.name,onChange:e=>qt({...Ut,name:e.target.value}),placeholder:n("enterSellerName","أدخل اسم البائع"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("usernameLabel","اسم المستخدم")," *"]}),t.jsx("input",{type:"text",value:Ut.username,onChange:e=>qt({...Ut,username:e.target.value}),placeholder:n("enterUsername","أدخل اسم المستخدم"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("passwordLabel","كلمة المرور")," *"]}),t.jsx("input",{type:"password",value:Ut.password,onChange:e=>qt({...Ut,password:e.target.value}),placeholder:n("enterPassword","أدخل كلمة المرور"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("phoneLabel","رقم الهاتف")}),t.jsx("input",{type:"tel",value:Ut.phone,onChange:e=>qt({...Ut,phone:e.target.value}),placeholder:n("enterPhone","+*********** 456")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("emailLabel","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:Ut.email,onChange:e=>qt({...Ut,email:e.target.value}),placeholder:n("enterEmail","أدخل البريد الإلكتروني")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("roleLabel","الدور")}),t.jsxs("select",{value:Ut.role,onChange:e=>qt({...Ut,role:e.target.value}),children:[t.jsx("option",{value:"seller",children:n("seller","بائع")}),t.jsx("option",{value:"admin",children:n("admin","مدير")})]})]}),t.jsx("div",{className:"form-group",children:t.jsxs("label",{className:"checkbox-label",children:[t.jsx("input",{type:"checkbox",checked:Ut.isActive,onChange:e=>qt({...Ut,isActive:e.target.checked})}),n("active","حساب نشط")]})})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!Ut.name||!Ut.username||!Ut.password)return void Jt(`⚠️ ${n("fillRequiredFields","يرجى ملء جميع الحقول المطلوبة")}`,"warning",3e3);const e=Ut.name.trim();if(Rt.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==Ut.id)))return void Jt(`❌ ${n("sellerNameAlreadyExists","اسم البائع موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);const t=Rt.find((e=>e.username===Ut.username&&e.id!==Ut.id));if(t)return void Jt(`❌ ${n("usernameExists","اسم المستخدم موجود مسبقاً")}: "${Ut.username}" - ${n("usedBySeller","مستخدم بواسطة")}: "${t.name}"`,"error",4e3);const a={...Ut,createdAt:(new Date).toISOString()},s=[...Rt,a];tn(s),Jt(`✅ ${n("sellerAddedSuccess","تم إضافة البائع")} ${Ut.name} ${n("sellerAddedSuccess","بنجاح!")}`,"success",3e3),$n()},children:["✅ ",n("addNewSeller","إضافة البائع")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:$n,children:["❌ ",n("cancel","إلغاء")]})]})]})}),Ve&&Je&&t.jsx("div",{className:"modal-overlay",onClick:xn,children:t.jsxs("div",{className:"modal-content invoice-modal",onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header",children:[t.jsxs("h2",{children:["📄 ",n("invoiceDetails","تفاصيل الفاتورة")," ",Je.invoiceNumber]}),t.jsx("button",{className:"modal-close",onClick:xn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"invoice-view",children:[t.jsxs("div",{className:"invoice-header-view",children:[t.jsx("div",{className:"store-info",children:t.jsxs("div",{className:"store-details",children:[t.jsx("h1",{children:Et.storeName}),t.jsxs("p",{children:["📞 ",Et.storePhone]}),t.jsxs("p",{children:["📍 ",Et.storeAddress]})]})}),t.jsxs("div",{className:"invoice-info",children:[t.jsx("h2",{children:n("salesInvoice","فاتورة مبيعات")}),t.jsxs("p",{children:[t.jsx("strong",{children:n("invoiceNumberColon","رقم الفاتورة:")})," ",Je.invoiceNumber]}),t.jsxs("p",{children:[t.jsx("strong",{children:n("dateColon","التاريخ:")})," ",Je.date]}),t.jsxs("p",{children:[t.jsx("strong",{children:n("creationTimeColon","وقت الإنشاء:")})," ",Je.createdAt||(new Date).toLocaleString("ar-DZ")]})]})]}),t.jsxs("div",{className:"customer-info",children:[t.jsxs("h3",{children:["👤 ",n("customerInfoTitle","معلومات العميل")]}),t.jsxs("div",{className:"info-grid",children:[t.jsxs("div",{className:"info-item",children:[t.jsx("strong",{children:n("customerNameColon","اسم العميل:")})," ",Je.customerName||n("walkInCustomer","زبون عابر")]}),t.jsxs("div",{className:"info-item",children:[t.jsx("strong",{children:n("paymentMethodColon","طريقة الدفع:")}),t.jsx("span",{className:"payment-method "+("نقداً"===Je.paymentMethod||"Espèces"===Je.paymentMethod||"En espèces"===Je.paymentMethod||"Cash"===Je.paymentMethod?"cash":"credit"),children:"نقداً"===Je.paymentMethod?n("cash","نقداً"):"دين"===Je.paymentMethod?n("credit","دين"):Je.paymentMethod||n("cash","نقداً")})]})]})]}),t.jsxs("div",{className:"invoice-items",children:[t.jsxs("h3",{children:["🛒 ",n("productsTitle","المنتجات")]}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("productColumn","المنتج")}),t.jsx("th",{children:n("quantityColumn","الكمية")}),t.jsx("th",{children:n("priceColumn","السعر")}),t.jsx("th",{children:n("totalColumn","المجموع")})]})}),t.jsx("tbody",{children:Je.items&&Je.items.length>0?Je.items.map(((e,n)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:e.quantity}),t.jsx("td",{children:Gt(e.price)}),t.jsx("td",{children:Gt(e.total)})]},n))):t.jsx("tr",{children:t.jsx("td",{colSpan:"4",style:{textAlign:"center",padding:"20px"},children:n("noProductsInInvoice","لا توجد منتجات في هذه الفاتورة")})})})]})})]}),t.jsx("div",{className:"invoice-totals",children:t.jsxs("div",{className:"totals-section",children:[t.jsxs("div",{className:"total-row",children:[t.jsx("span",{children:n("subtotalColon","المجموع الفرعي:")}),t.jsx("span",{children:Gt(Je.total||0)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("taxColon","الضريبة")," (",Et.taxRate,"%):"]}),t.jsx("span",{children:Gt(Je.tax||0)})]}),t.jsxs("div",{className:"total-row",children:[t.jsx("span",{children:n("discountColon","الخصم:")}),t.jsx("span",{children:Gt(Je.discount||0)})]}),t.jsxs("div",{className:"total-row final-total",children:[t.jsx("span",{children:n("finalTotalColon","المجموع النهائي:")}),t.jsx("span",{children:Gt(Je.finalTotal||0)})]})]})})]})}),t.jsxs("div",{className:"modal-footer",children:[t.jsxs("button",{className:"btn btn-secondary",onClick:()=>pn(Je),title:n("normalPrintButton","طباعة عادية"),children:["🖨️ ",n("normalPrintButton","طباعة عادية")]}),t.jsxs("button",{className:"btn btn-success",onClick:()=>hn(Je),title:n("thermalPrintButton","طباعة حرارية"),children:["🧾 ",n("thermalPrintButton","طباعة حرارية")]}),t.jsxs("button",{className:"btn btn-primary",onClick:xn,children:["✅ ",n("closeButton","إغلاق")]})]})]})}),t.jsxs("div",{className:"toast-container",children:[qe.map((e=>t.jsxs("div",{className:`toast toast-${e.type}`,onClick:t=>{t.preventDefault(),t.stopPropagation(),_t(e.id)},children:[t.jsxs("div",{className:"toast-content",children:[t.jsx("span",{className:"toast-message",children:e.message}),t.jsx("button",{className:"toast-close",onClick:t=>{t.preventDefault(),t.stopPropagation(),_t(e.id)},onMouseDown:e=>{e.preventDefault(),e.stopPropagation()},onTouchStart:e=>{e.preventDefault(),e.stopPropagation()},children:"×"})]}),t.jsx("div",{className:"toast-progress",children:t.jsx("div",{className:"toast-progress-bar",style:{animation:`toast-progress ${e.duration}ms linear forwards`}})})]},e.id))),qe.length>2&&t.jsx("div",{className:"toast-clear-all",children:t.jsxs("button",{className:"btn btn-sm btn-secondary",onClick:e=>{e.preventDefault(),e.stopPropagation(),Ge([])},children:["🗑️ ",n("clearAllNotifications","مسح جميع الإشعارات")]})})]}),ft&&t.jsx("div",{className:"modal-overlay",onClick:wn,children:t.jsxs("div",{className:`modal-content settings-modal lang-${N}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==N?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["⚙️ ",n("storeSettingsModal","إعدادات المتجر")]}),t.jsx("button",{className:"modal-close",onClick:wn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["🏪 ",n("storeNameRequired","اسم المتجر")," *"]}),t.jsx("input",{type:"text",value:Et.storeName,onChange:e=>At({...Et,storeName:e.target.value}),placeholder:n("enterStoreName","أدخل اسم المتجر"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["🏷️ ",n("storeNumberLabel","رقم المتجر")]}),t.jsx("input",{type:"text",value:Et.storeNumber,onChange:e=>At({...Et,storeNumber:e.target.value}),placeholder:n("storeNumberPlaceholder","ST001")})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["📞 ",n("phoneNumberLabel","رقم الهاتف")]}),t.jsx("input",{type:"tel",value:Et.storePhone,onChange:e=>At({...Et,storePhone:e.target.value}),placeholder:n("phoneNumberPlaceholder","+*********** 456")})]}),t.jsxs("div",{className:"form-group full-width",children:[t.jsxs("label",{children:["📍 ",n("addressLabel","العنوان")]}),t.jsx("input",{type:"text",value:Et.storeAddress,onChange:e=>At({...Et,storeAddress:e.target.value}),placeholder:n("fullStoreAddress","العنوان الكامل للمتجر")})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💰 ",n("taxRateLabel","معدل الضريبة (%)")]}),t.jsx("input",{type:"number",value:Et.taxRate,onChange:e=>At({...Et,taxRate:parseFloat(e.target.value)||0}),placeholder:n("taxRatePlaceholder","19"),min:"0",max:"100",step:"0.1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💱 ",n("currencyLabel","العملة")]}),t.jsxs("select",{value:Et.currency,onChange:e=>At({...Et,currency:e.target.value}),children:[t.jsx("option",{value:"DZD",children:n("algerianDinar","دينار جزائري (DZD)")}),t.jsx("option",{value:"USD",children:n("usDollar","دولار أمريكي (USD)")}),t.jsx("option",{value:"EUR",children:n("euro","يورو (EUR)")})]})]}),t.jsxs("div",{className:"form-group full-width",children:[t.jsxs("label",{children:["🖼️ ",n("storeLogo","شعار المتجر")]}),t.jsx("input",{type:"file",accept:"image/*",onChange:e=>{const t=e.target.files[0];if(t){const e=new FileReader;e.onload=e=>{At({...Et,storeLogo:e.target.result})},e.readAsDataURL(t)}}}),Et.storeLogo&&t.jsx("div",{className:"logo-preview",children:t.jsx("img",{src:Et.storeLogo,alt:n("logoPreview","شعار المتجر")})})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==N?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{var e;e=Et,localStorage.setItem("icaldz-settings",JSON.stringify(e)),At(e),Jt(`✅ ${n("storeSettingsSaved","تم حفظ إعدادات المتجر بنجاح")}`,"success",3e3),wn()},children:["✅ ",n("saveSettings","حفظ الإعدادات")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:wn,children:["❌ ",n("cancel","إلغاء")]})]})]})}),t.jsx("div",{className:"toast-container",children:qe.map((e=>t.jsxs("div",{className:`toast toast-${e.type}`,onClick:()=>_t(e.id),children:[t.jsx("div",{className:"toast-content",children:e.message}),t.jsx("button",{className:"toast-close",onClick:t=>{t.stopPropagation(),_t(e.id)},children:"×"})]},e.id)))}),q&&t.jsx("button",{className:"scroll-to-top",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},title:n("scrollToTop","العودة إلى الأعلى"),children:"↑"}),P&&!M&&t.jsx(j,{onActivationSuccess:e=>{T(!0),console.log("Program activated successfully:",e)}})]}):t.jsxs("div",{className:"login-container",children:[t.jsxs("div",{className:"login-card",children:[t.jsxs("div",{className:"login-header",children:[t.jsx("div",{className:"login-avatar",children:"👤"}),t.jsx("h2",{children:n("welcome","مرحباً بك")}),t.jsx("p",{children:n("loginPrompt","قم بتسجيل الدخول للوصول إلى حسابك")})]}),t.jsxs("form",{onSubmit:bs,className:"login-form",children:[t.jsx("div",{className:"form-group",children:t.jsx("input",{type:"text",placeholder:n("username","اسم المستخدم"),value:ke.username,onChange:e=>Ce({...ke,username:e.target.value}),required:!0})}),t.jsx("div",{className:"form-group",children:t.jsx("input",{type:"password",placeholder:n("password","كلمة المرور"),value:ke.password,onChange:e=>Ce({...ke,password:e.target.value}),required:!0})}),t.jsx("div",{className:"form-group",children:t.jsxs("label",{className:"checkbox-label",children:[t.jsx("input",{type:"checkbox"}),n("rememberMe","تذكرني")]})}),t.jsx("button",{type:"submit",className:"login-btn",children:n("login","تسجيل الدخول")})]}),t.jsxs("div",{className:"login-footer",children:[t.jsxs("p",{children:["© 2025 ",n("systemName","نظام المحاسبي")," - ",n("allRightsReserved","جميع الحقوق محفوظة")]}),t.jsxs("p",{children:[n("version","الإصدار")," 1.0.0"]})]})]}),t.jsx("div",{className:"login-info",children:t.jsxs("div",{className:"system-info",children:[t.jsx("div",{className:"system-icon",children:"🏛️"}),t.jsx("h1",{children:n("systemName","نظام المحاسبي")}),t.jsx("p",{children:n("systemDescription","النظام المحاسبي المتكامل لإدارة أعمالك")}),t.jsxs("div",{className:"features-list",children:[t.jsxs("div",{className:"feature",children:["✅ ",n("feature1","إدارة المبيعات والمشتريات بكفاءة عالية")]}),t.jsxs("div",{className:"feature",children:["✅ ",n("feature2","إدارة المخزون ومراقبة الأصناف")]}),t.jsxs("div",{className:"feature",children:["✅ ",n("feature3","التقارير المالية والمحاسبية المتكاملة")]}),t.jsxs("div",{className:"feature",children:["✅ ",n("feature4","إدارة العملاء والموردين بسهولة")]})]})]})})]}):t.jsx(g,{onLanguageSelected:V})}function w(){return t.jsx(x,{children:t.jsx(N,{})})}n.createRoot(document.getElementById("root")).render(t.jsx(a.StrictMode,{children:t.jsx(w,{})}));
