import React, { useState, useRef, useEffect } from 'react';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLanguage } from '../LanguageContext.jsx';

const Dashboard = () => {
  const {
    storeSettings,
    savedInvoices,
    products,
    setCurrentPage,
    showToast,
    formatPrice,
    currentUser,
    setSalesInvoice,
    setShowSalesModal,
    openSalesModal,
    openProductModal,
    openPurchaseModal
  } = useAppState();

  const { t, language: currentLanguage } = useLanguage();

  // Dashboard-specific state
  const [dashboardScannerInput, setDashboardScannerInput] = useState('');
  const [dashboardTotalDisplay, setDashboardTotalDisplay] = useState(null);
  const [lastSavedInvoice, setLastSavedInvoice] = useState(null);
  const dashboardScannerRef = useRef(null);

  // Auto-focus dashboard barcode input
  useEffect(() => {
    if (dashboardScannerRef.current) {
      setTimeout(() => {
        dashboardScannerRef.current.focus();
        console.log('🎯 Dashboard: Auto-focused barcode input');
      }, 200);
    }
  }, []);

  // Load last saved invoice on mount
  useEffect(() => {
    if (savedInvoices.length > 0) {
      const lastInvoice = savedInvoices[savedInvoices.length - 1];
      setLastSavedInvoice(lastInvoice);
    }
  }, [savedInvoices]);

  // Dashboard statistics
  const dashboardStats = [
    {
      id: 1,
      title: t('totalSales', 'إجمالي المبيعات'),
      value: formatPrice(savedInvoices.reduce((sum, inv) => sum + inv.finalTotal, 0)),
      unit: '',
      icon: '💰',
      color: 'green'
    },
    {
      id: 2,
      title: t('totalInvoices', 'عدد الفواتير'),
      value: savedInvoices.length,
      unit: t('invoices', 'فاتورة'),
      icon: '📄',
      color: 'blue'
    },
    {
      id: 3,
      title: t('totalProducts', 'إجمالي المنتجات'),
      value: products.length,
      unit: t('products', 'منتج'),
      icon: '📦',
      color: 'orange'
    },
    {
      id: 4,
      title: t('lowStockProducts', 'منتجات منخفضة المخزون'),
      value: products.filter(p => p.stock <= p.minStock).length,
      unit: t('products', 'منتج'),
      icon: '⚠️',
      color: 'red'
    }
  ];

  // Sample invoices for dashboard
  const sampleInvoices = savedInvoices.slice(0, 5).map(invoice => ({
    id: invoice.invoiceNumber,
    date: invoice.date,
    supplier: translateCustomerName(invoice.customerName),
    paid: invoice.finalTotal,
    amount: invoice.finalTotal,
    status: t('paid', 'مدفوعة')
  }));

  // Translate customer name based on language
  const translateCustomerName = (customerName) => {
    if (customerName === 'زبون عابر' && currentLanguage === 'fr') return 'Client de passage';
    if (customerName === 'زبون عابر' && currentLanguage === 'en') return 'Walk-in Customer';
    if (customerName === 'Client de passage' && currentLanguage === 'ar') return 'زبون عابر';
    if (customerName === 'Walk-in Customer' && currentLanguage === 'ar') return 'زبون عابر';
    return customerName;
  };

  // Check if scanned code is valid
  const isValidScannedCode = (code) => {
    return code && code.trim().length > 0;
  };

  // Handle dashboard scanner input
  const handleDashboardScannerInput = (e) => {
    setDashboardScannerInput(e.target.value);
  };

  // Handle dashboard scanner key press
  const handleDashboardScannerKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const barcode = dashboardScannerInput.trim();

      if (barcode && isValidScannedCode(barcode)) {
        const foundProduct = products.find(p => p.barcode === barcode && barcode.trim() !== '');
        if (foundProduct) {
          console.log('🏠 Dashboard: Opening sales invoice with product:', foundProduct.name);
          openInvoiceWithProduct(foundProduct);
        } else {
          showToast(`❌ ${t('productNotFound', 'لم يتم العثور على المنتج بالباركود')}: ${barcode}`, 'error', 2000);
        }
      } else {
        showToast(`⚠️ ${t('pleaseEnterBarcode', 'يرجى إدخال الباركود أولاً')}`, 'warning', 2000);
      }
    }
  };

  // Open invoice with scanned product
  const openInvoiceWithProduct = (foundProduct) => {
    // Clear dashboard scanner input
    setDashboardScannerInput('');

    // Create new invoice object
    const newInvoice = {
      invoiceNumber: 'INV-' + Date.now(),
      date: new Date().toISOString().split('T')[0],
      customerId: 'GUEST',
      customerName: t('walkInCustomer', 'زبون عابر'),
      paymentMethod: 'نقداً',
      items: [],
      total: 0,
      discount: 0,
      tax: 0,
      finalTotal: 0
    };

    // Reset sales invoice and open modal
    setSalesInvoice(newInvoice);
    setShowSalesModal(true);

    // Add product to invoice after state has been set
    setTimeout(() => {
      console.log('🏠 Dashboard: Adding product to new sales invoice:', foundProduct.name);

      // Create new item for the scanned product
      const newItem = {
        productId: foundProduct.id,
        productName: foundProduct.name,
        quantity: 1,
        price: foundProduct.sellPrice || foundProduct.price,
        total: foundProduct.sellPrice || foundProduct.price
      };

      // Calculate totals
      const total = newItem.total;
      const tax = total * (storeSettings.taxRate / 100);
      const finalTotal = total + tax;

      // Update invoice with the new product
      setSalesInvoice({
        ...newInvoice,
        items: [newItem],
        total,
        tax,
        finalTotal
      });

      console.log('🏠 Dashboard: Product added to new invoice:', foundProduct.name);
    }, 300);

    showToast(`🛒 ${t('openingSalesInvoice', 'فتح فاتورة المبيعات')} - ${foundProduct.name}`, 'success', 2000);
  };

  return (
    <div className="dashboard">
      <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
        <div className="page-title-section">
          <h1>🏠 {t('dashboard', 'لوحة التحكم')}</h1>
        </div>
      </div>

      {/* Combined Scanner and LCD Frame - ABOVE the 3 buttons */}
      <div className="dashboard-scanner-lcd-unified">
        <div className="unified-frame">
          {/* Scanner Section */}
          <div className="scanner-section">
            <h3>📷 {t('scanBarcode', 'مسح الباركود')} - <span className="scanner-status-active">{t('active', 'نشط')}</span></h3>
            <div className="barcode-input-container">
              <span className="barcode-icon">📷</span>
              <input
                type="text"
                placeholder={t('scanBarcodeToAddProduct', 'امسح الباركود - اضغط Enter لفتح الفاتورة')}
                value={dashboardScannerInput}
                onChange={handleDashboardScannerInput}
                onKeyDown={handleDashboardScannerKeyPress}
                className="barcode-input"
                ref={dashboardScannerRef}
                autoFocus
              />
            </div>
            <div className="barcode-actions">
              <button
                type="button"
                className="btn btn-success btn-sm"
                onClick={() => {
                  const barcode = dashboardScannerInput.trim();
                  if (barcode && isValidScannedCode(barcode)) {
                    const foundProduct = products.find(p => p.barcode === barcode && barcode.trim() !== '');
                    if (foundProduct) {
                      console.log('🏠 Dashboard: Opening sales invoice with product:', foundProduct.name);
                      openInvoiceWithProduct(foundProduct);
                    } else {
                      showToast(`❌ ${t('productNotFound', 'لم يتم العثور على المنتج بالباركود')}: ${barcode}`, 'error', 2000);
                    }
                  } else {
                    showToast(`⚠️ ${t('pleaseEnterBarcode', 'يرجى إدخال الباركود أولاً')}`, 'warning', 2000);
                  }
                }}
                title={t('openInvoiceWithProduct', 'فتح الفاتورة مع المنتج')}
              >
                🛒 {t('openInvoice', 'فتح الفاتورة')}
              </button>
              <button
                type="button"
                className="btn btn-warning btn-sm"
                onClick={() => setDashboardScannerInput('')}
                title={t('clearBarcode', 'مسح الباركود')}
              >
                🗑️ {t('clear', 'مسح')}
              </button>
            </div>
            <small className="barcode-help" style={{color: '#7fb3d3', marginTop: '10px', display: 'block', textAlign: 'center', fontSize: '12px', fontStyle: 'italic'}}>
              💡 {t('dashboardBarcodeHelp', 'امسح الباركود لعرض المعلومات، اضغط Enter أو زر فتح الفاتورة لإضافة المنتج للفاتورة')}
            </small>
          </div>

          {/* LCD Section - ONLY SHOWS TOTAL FINAL AMOUNTS */}
          <div className="lcd-section">
            <div className="lcd-screen">
              <div className="lcd-header">
                <span className="lcd-title">💰 {t('totalFinal', 'المجموع النهائي')}</span>
                <span className="lcd-status">{(dashboardTotalDisplay || lastSavedInvoice) ? '●' : '○'}</span>
              </div>
              <div className="lcd-content">
                {dashboardTotalDisplay ? (
                  // Temporary display for 6 seconds after saving
                  <div className="total-final-display">
                    <div className="total-final-label">{t('finalTotal', 'المجموع النهائي')}:</div>
                    <div className="total-final-amount">{formatPrice(dashboardTotalDisplay.finalTotal)}</div>
                    <div className="total-final-currency">DZD</div>
                    <div className="invoice-info">
                      <div className="invoice-number">{dashboardTotalDisplay.invoiceNumber}</div>
                      <div className="timestamp">{dashboardTotalDisplay.timestamp}</div>
                    </div>
                  </div>
                ) : lastSavedInvoice ? (
                  // Persistent display of last saved invoice
                  <div className="total-final-display">
                    <div className="total-final-label">{t('lastInvoice', 'آخر فاتورة')}:</div>
                    <div className="total-final-amount">{formatPrice(lastSavedInvoice.finalTotal)}</div>
                    <div className="total-final-currency">DZD</div>
                    <div className="invoice-info">
                      <div className="invoice-number">{lastSavedInvoice.invoiceNumber}</div>
                      <div className="timestamp">{lastSavedInvoice.date} - {lastSavedInvoice.timestamp}</div>
                    </div>
                  </div>
                ) : (
                  // Default placeholder when no invoices exist
                  <div className="display-placeholder">
                    <div className="placeholder-text">{t('scanBarcodeToOpenInvoice', 'امسح الباركود واضغط Enter لفتح الفاتورة')}</div>
                    <div className="placeholder-price-big">--- {storeSettings.currency}</div>
                    <div className="placeholder-scanner-hint">📷 {t('scannerActive', 'الماسح نشط')}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Action Buttons Row - BELOW Scanner/LCD Frame */}
      <div className="dashboard-action-buttons">
        <button
          className="action-btn action-btn-sales"
          onClick={openSalesModal}
          title="F1"
        >
          <div className="btn-shortcut">F1</div>
          <div className="btn-icon">🛒</div>
          <div className="btn-text">{t('newSalesInvoice', 'فاتورة مبيعات جديدة')}</div>
        </button>

        <button
          className="action-btn action-btn-product"
          onClick={openProductModal}
          title="F6"
        >
          <div className="btn-shortcut">F6</div>
          <div className="btn-icon">📦</div>
          <div className="btn-text">{t('addNewProduct', 'إضافة منتج جديد')}</div>
        </button>

        <button
          className="action-btn action-btn-purchase"
          onClick={() => {
            setCurrentPage('purchases');
            setTimeout(() => {
              openPurchaseModal();
            }, 100);
          }}
          title="F7"
        >
          <div className="btn-shortcut">F7</div>
          <div className="btn-icon">📦</div>
          <div className="btn-text">{t('newPurchaseInvoice', 'فاتورة مشتريات جديدة')}</div>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        {dashboardStats.map(stat => (
          <div key={stat.id} className={`stat-card ${stat.color}`}>
            <div className="stat-icon">{stat.icon}</div>
            <div className="stat-content">
              <h3>{stat.title}</h3>
              <div className="stat-value">
                {stat.value} <span className="stat-unit">{stat.unit}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Transactions */}
      <div className="dashboard-section">
        <h2>{t('recentInvoicesAndOperations', 'آخر الفواتير والعمليات')}</h2>
        <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th>{t('invoiceNumber', 'رقم الفاتورة')}</th>
                  <th>{t('date', 'التاريخ')}</th>
                  <th>{t('supplier', 'المورد')}</th>
                  <th>{t('amountPaid', 'المبلغ المدفوع')}</th>
                  <th>{t('totalAmount', 'المبلغ الإجمالي')}</th>
                  <th>{t('status', 'الحالة')}</th>
                </tr>
              </thead>
              <tbody>
                {sampleInvoices.map(invoice => (
                  <tr key={invoice.id}>
                    <td>{invoice.id}</td>
                    <td>{invoice.date}</td>
                    <td>{translateCustomerName(invoice.supplier)}</td>
                    <td>{formatPrice(invoice.paid)}</td>
                    <td>{formatPrice(invoice.amount)}</td>
                    <td>
                      <span className={`status ${invoice.status === 'مدفوعة' ? 'paid' : 'partial'}`}>
                        {invoice.status === 'مدفوعة' ? t('paid', 'مدفوعة') : invoice.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
        </div>

        <div className="actions-header">
          <h3>{t('quickOperations', 'العمليات السريعة')}</h3>
          <p>{t('chooseOperation', 'اختر العملية التي تريد تنفيذها')}</p>
        </div>

        <div className="table-actions">
          <button
            className="btn btn-success btn-large quick-operation-btn"
            onClick={openSalesModal}
            title={t('newSalesInvoice', 'إنشاء فاتورة بيع جديدة')}
          >
              <span className="btn-icon">�</span>
            <span className="btn-text">{t('newSalesInvoice', 'فاتورة مبيعات جديدة')}</span>
          </button>
          <button
            className="btn btn-primary quick-operation-btn"
            onClick={() => {
              setCurrentPage('purchases');
              setTimeout(() => {
                openPurchaseModal();
              }, 100);
              showToast(`📄 ${t('f7NewPurchaseInvoiceOpened', 'تم فتح فاتورة مشتريات جديدة')}`, 'success', 2000);
            }}
            title={t('newPurchaseInvoice', 'إنشاء فاتورة مشتريات جديدة')}
          >
            <span className="btn-icon">📄</span>
            <span className="btn-text">{t('newPurchaseInvoice', 'فاتورة مشتريات جديدة')}</span>
          </button>
          <button
            className="btn btn-secondary quick-operation-btn"
            onClick={() => {
              setCurrentPage('purchases');
              showToast(`📊 ${t('purchaseReportClicked', 'تم الانتقال لصفحة المشتريات')}`, 'success', 2000);
            }}
            title={t('purchaseReport', 'عرض تقرير المشتريات')}
          >
            <span className="btn-icon">📊</span>
            <span className="btn-text">{t('purchaseReport', 'تقرير المشتريات')}</span>
          </button>
          <button
            className="btn btn-info quick-operation-btn"
            onClick={() => {
              if (currentUser.role === 'مدير' || currentUser.role === 'admin') {
                setCurrentPage('reports');
                showToast(`📈 ${t('purchaseStatisticsClicked', 'تم الانتقال لصفحة التقارير والإحصائيات')}`, 'success', 2000);
              } else {
                showToast(`🔒 ${t('reportsManagerOnly', 'التقارير والإحصائيات متاحة للمدير فقط')}`, 'warning', 3000);
              }
            }}
            title={t('purchaseStatistics', 'عرض إحصائيات المشتريات')}
          >
            <span className="btn-icon">📈</span>
            <span className="btn-text">{t('purchaseStatistics', 'إحصائيات المشتريات')}</span>
          </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
