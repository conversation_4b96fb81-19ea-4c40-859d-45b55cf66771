const e=new class{constructor(){this.sounds={},this.isEnabled=!0,this.volume=.7,this.isInitialized=!1,this.soundDefinitions={newInvoice:{file:"/sounds/new-invoice.mp3",description:"🛒 فاتورة جديدة",fallback:"beep"},saveInvoice:{file:"/sounds/save-invoice.mp3",description:"💾 حفظ الفاتورة",fallback:"beep"},printInvoice:{file:"/sounds/print.mp3",description:"🖨️ طباعة الفاتورة",fallback:"beep"},closeWindow:{file:"/sounds/close.mp3",description:"❌ إغلاق النافذة",fallback:"click"},success:{file:"/sounds/success.mp3",description:"✅ نجاح العملية",fallback:"beep"},warning:{file:"/sounds/warning.mp3",description:"⚠️ تحذير",fallback:"beep"},error:{file:"/sounds/error.mp3",description:"❌ خطأ",fallback:"beep"},addProduct:{file:"/sounds/add-product.mp3",description:"➕ إضافة منتج",fallback:"click"},deleteProduct:{file:"/sounds/delete.mp3",description:"🗑️ حذف منتج",fallback:"click"},quickSearch:{file:"/sounds/search.mp3",description:"🔍 البحث السريع",fallback:"click"},refresh:{file:"/sounds/refresh.mp3",description:"🔄 تحديث البيانات",fallback:"click"},shortcutActivated:{file:"/sounds/shortcut.mp3",description:"⌨️ اختصار لوحة المفاتيح",fallback:"click"}},this.init()}async init(){try{this.loadPreferences(),await this.preloadSounds(["success","error","newInvoice","saveInvoice"]),this.isInitialized=!0,console.log("🔊 Sound Manager initialized successfully")}catch(e){console.warn("🔇 Sound Manager initialization failed, using fallback mode:",e),this.isInitialized=!0}}async preloadSounds(e){const n=e.map((e=>this.loadSound(e)));await Promise.allSettled(n)}async loadSound(e){if(this.sounds[e])return this.sounds[e];const n=this.soundDefinitions[e];if(!n)return console.warn(`🔇 Sound definition not found: ${e}`),null;try{const i=new Audio(n.file);return i.volume=this.volume,i.preload="auto",await new Promise(((e,n)=>{i.addEventListener("canplaythrough",e,{once:!0}),i.addEventListener("error",n,{once:!0}),i.load()})),this.sounds[e]=i,i}catch(i){return console.warn(`🔇 Failed to load sound ${e}:`,i),null}}async play(e,n={}){if(!this.isEnabled)return;const{volume:i=this.volume,force:t=!1,showNotification:s=!0}=n;try{let n=this.sounds[e];if(n||t||(n=await this.loadSound(e)),n)return n.volume=Math.min(Math.max(i,0),1),n.currentTime=0,await n.play(),s&&this.showSoundNotification(e),!0}catch(o){console.warn(`🔇 Failed to play sound ${e}:`,o)}return this.playFallbackSound(e),!1}playFallbackSound(e){const n=this.soundDefinitions[e];if(n)try{"beep"===n.fallback?this.createBeep(800,150):"click"===n.fallback&&this.createBeep(1200,50)}catch(i){console.warn("🔇 Fallback sound failed:",i)}}createBeep(e=800,n=150){try{const i=new(window.AudioContext||window.webkitAudioContext),t=i.createOscillator(),s=i.createGain();t.connect(s),s.connect(i.destination),t.frequency.value=e,t.type="sine",s.gain.setValueAtTime(0,i.currentTime),s.gain.linearRampToValueAtTime(.3*this.volume,i.currentTime+.01),s.gain.exponentialRampToValueAtTime(.001,i.currentTime+n/1e3),t.start(i.currentTime),t.stop(i.currentTime+n/1e3)}catch(i){console.warn("🔇 Web Audio API beep failed:",i)}}showSoundNotification(e){const n=this.soundDefinitions[e];if(!n)return;const i=document.createElement("div");if(i.className="sound-notification",i.textContent=n.description,i.style.cssText="\n      position: fixed;\n      top: 20px;\n      left: 20px;\n      background: rgba(22, 160, 133, 0.9);\n      color: white;\n      padding: 8px 16px;\n      border-radius: 20px;\n      font-size: 12px;\n      font-weight: 600;\n      z-index: 10000;\n      animation: soundNotificationSlide 2s ease-out forwards;\n      pointer-events: none;\n    ",!document.getElementById("sound-notification-styles")){const e=document.createElement("style");e.id="sound-notification-styles",e.textContent="\n        @keyframes soundNotificationSlide {\n          0% { transform: translateX(-100%); opacity: 0; }\n          20% { transform: translateX(0); opacity: 1; }\n          80% { transform: translateX(0); opacity: 1; }\n          100% { transform: translateX(-100%); opacity: 0; }\n        }\n      ",document.head.appendChild(e)}document.body.appendChild(i),setTimeout((()=>{i.parentNode&&i.parentNode.removeChild(i)}),2e3)}loadPreferences(){try{const e=localStorage.getItem("icaldz-sound-preferences");if(e){const n=JSON.parse(e);this.isEnabled=!1!==n.isEnabled,this.volume=n.volume||.7}}catch(e){console.warn("🔇 Failed to load sound preferences:",e)}}savePreferences(){try{const e={isEnabled:this.isEnabled,volume:this.volume};localStorage.setItem("icaldz-sound-preferences",JSON.stringify(e))}catch(e){console.warn("🔇 Failed to save sound preferences:",e)}}toggle(){return this.isEnabled=!this.isEnabled,this.savePreferences(),this.isEnabled}setVolume(e){this.volume=Math.min(Math.max(e,0),1),Object.values(this.sounds).forEach((e=>{e&&(e.volume=this.volume)})),this.savePreferences()}getStatus(){return{isEnabled:this.isEnabled,volume:this.volume,isInitialized:this.isInitialized,loadedSounds:Object.keys(this.sounds).length}}};export{e as S};
