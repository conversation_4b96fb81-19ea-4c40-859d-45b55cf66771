import React, { useState, useEffect } from 'react';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLanguage } from '../LanguageContext.jsx';

const SalesManagement = () => {
  const {
    savedInvoices,
    setSavedInvoices,
    currentUser,
    storeSettings,
    showToast,
    formatPrice
  } = useAppState();

  const { t, language: currentLanguage } = useLanguage();

  // Sales-specific state
  const [selectedSalesInvoices, setSelectedSalesInvoices] = useState([]);
  const [salesInvoiceFilter, setSalesInvoiceFilter] = useState('');
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  // Filter sales invoices based on customer name or invoice number
  const filteredSalesInvoices = savedInvoices.filter(invoice =>
    invoice.customerName?.toLowerCase().includes(salesInvoiceFilter.toLowerCase()) ||
    invoice.invoiceNumber?.toLowerCase().includes(salesInvoiceFilter.toLowerCase())
  );

  // Toggle select all functionality
  const toggleSelectAll = () => {
    if (selectedSalesInvoices.length === filteredSalesInvoices.length) {
      setSelectedSalesInvoices([]);
    } else {
      setSelectedSalesInvoices(filteredSalesInvoices.map(invoice => invoice.id));
    }
  };

  // Toggle individual item selection
  const toggleSelectItem = (invoiceId) => {
    setSelectedSalesInvoices(prev =>
      prev.includes(invoiceId)
        ? prev.filter(id => id !== invoiceId)
        : [...prev, invoiceId]
    );
  };

  // Delete selected invoices
  const deleteSelectedItems = () => {
    if (selectedSalesInvoices.length === 0) return;

    if (window.confirm(t('confirmDeleteSelected', 'هل أنت متأكد من حذف العناصر المحددة؟'))) {
      const updatedInvoices = savedInvoices.filter(invoice =>
        !selectedSalesInvoices.includes(invoice.id)
      );
      setSavedInvoices(updatedInvoices);
      localStorage.setItem('icaldz-invoices', JSON.stringify(updatedInvoices));
      setSelectedSalesInvoices([]);
      showToast(`✅ ${t('selectedItemsDeleted', 'تم حذف العناصر المحددة')}`, 'success', 3000);
    }
  };

  // View invoice details
  const viewInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  // Print invoice (normal)
  const printInvoice = (invoice) => {
    const printWindow = window.open('', '_blank');
    const invoiceHtml = generateInvoiceHTML(invoice);
    printWindow.document.write(invoiceHtml);
    printWindow.document.close();
    printWindow.print();
  };

  // Generate invoice HTML for printing
  const generateInvoiceHTML = (invoice) => {
    const isRTL = currentLanguage === 'ar';
    return `
      <!DOCTYPE html>
      <html dir="${isRTL ? 'rtl' : 'ltr'}">
      <head>
        <meta charset="UTF-8">
        <title>${t('invoice', 'فاتورة')} ${invoice.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .invoice-header { text-align: center; margin-bottom: 30px; }
          .invoice-details { margin-bottom: 20px; }
          .invoice-table { width: 100%; border-collapse: collapse; }
          .invoice-table th, .invoice-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: ${isRTL ? 'right' : 'left'};
          }
          .invoice-table th { background-color: #f2f2f2; }
          .total-row { font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="invoice-header">
          <h1>${storeSettings.storeName}</h1>
          <h2>${t('invoice', 'فاتورة')} ${invoice.invoiceNumber}</h2>
        </div>
        <div class="invoice-details">
          <p><strong>${t('date', 'التاريخ')}:</strong> ${invoice.date}</p>
          <p><strong>${t('customer', 'الزبون')}:</strong> ${invoice.customerName}</p>
          <p><strong>${t('paymentMethod', 'طريقة الدفع')}:</strong> ${invoice.paymentMethod}</p>
        </div>
        <table class="invoice-table">
          <thead>
            <tr>
              <th>${t('product', 'المنتج')}</th>
              <th>${t('quantity', 'الكمية')}</th>
              <th>${t('price', 'السعر')}</th>
              <th>${t('total', 'المجموع')}</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map(item => `
              <tr>
                <td>${item.productName}</td>
                <td>${item.quantity}</td>
                <td>${formatPrice(item.price)}</td>
                <td>${formatPrice(item.total)}</td>
              </tr>
            `).join('')}
          </tbody>
          <tfoot>
            <tr class="total-row">
              <td colspan="3">${t('finalTotal', 'المجموع النهائي')}</td>
              <td>${formatPrice(invoice.finalTotal)}</td>
            </tr>
          </tfoot>
        </table>
      </body>
      </html>
    `;
  };

  // Print thermal invoice
  const printThermalInvoice = (invoice) => {
    // This would integrate with thermal printer
    showToast(`🧾 ${t('thermalPrintSent', 'تم إرسال الطباعة الحرارية')}`, 'info', 3000);
  };

  // Translate customer name based on language
  const translateCustomerName = (customerName) => {
    if (customerName === 'زبون عابر' && currentLanguage === 'fr') return 'Client de passage';
    if (customerName === 'زبون عابر' && currentLanguage === 'en') return 'Walk-in Customer';
    if (customerName === 'Client de passage' && currentLanguage === 'ar') return 'زبون عابر';
    if (customerName === 'Walk-in Customer' && currentLanguage === 'ar') return 'زبون عابر';
    return customerName;
  };

  return (
    <div className="sales-page">
      <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
        <div className="page-title-section">
          <h1>🛒 {t('salesManagement', 'إدارة المبيعات')}</h1>
        </div>
        <div className="page-description-section">
          <div className="header-actions">
            <button className="btn btn-info btn-compact" onClick={() => showToast('📊 Report feature coming soon', 'info', 2000)}>
              📊 {t('report', 'تقرير')}
            </button>
          </div>
        </div>
      </div>

      {/* Sales Summary Cards - Floating */}
      <div className="sales-summary">
        <div className="summary-card green">
          <h3>{t('totalSales', 'إجمالي المبيعات')}</h3>
          <div className="summary-value">{formatPrice(savedInvoices.reduce((sum, inv) => sum + inv.finalTotal, 0))}</div>
        </div>
        <div className="summary-card blue">
          <h3>{t('invoiceCount', 'عدد الفواتير')}</h3>
          <div className="summary-value">{savedInvoices.length}</div>
        </div>
        <div className="summary-card orange">
          <h3>{t('averageInvoice', 'متوسط الفاتورة')}</h3>
          <div className="summary-value">
            {savedInvoices.length > 0
              ? formatPrice(savedInvoices.reduce((sum, inv) => sum + inv.finalTotal, 0) / savedInvoices.length)
              : formatPrice(0)
            }
          </div>
        </div>
        <div className="summary-card purple">
          <h3>{t('creditInvoices', 'فواتير دين')}</h3>
          <div className="summary-value">{savedInvoices.filter(inv => inv.paymentMethod === 'دين' || inv.paymentMethod === 'Dette' || inv.paymentMethod === 'Credit').length}</div>
        </div>
      </div>

      {/* Bulk Actions for Sales */}
      {filteredSalesInvoices.length > 0 && (
        <div className={`bulk-actions ${selectedSalesInvoices.length > 0 ? 'has-selection' : ''}`}>
          <div className="select-all-container">
            <input
              type="checkbox"
              className="select-all-checkbox"
              checked={filteredSalesInvoices.length > 0 && selectedSalesInvoices.length === filteredSalesInvoices.length}
              onChange={toggleSelectAll}
              id="select-all-sales"
            />
            <label htmlFor="select-all-sales" className="select-all-label">
              {t('selectAll', 'تحديد الكل')}
            </label>
            {selectedSalesInvoices.length > 0 && (
              <span className="selected-count">
                ({selectedSalesInvoices.length} {t('selected', 'محدد')})
              </span>
            )}
          </div>
          {selectedSalesInvoices.length > 0 && (currentUser.role === 'مدير' || currentUser.role === 'admin') && (
            <button
              className="bulk-delete-btn"
              onClick={deleteSelectedItems}
              title={t('deleteSelected', 'حذف المحدد')}
            >
              🗑️ {t('deleteSelected', 'حذف المحدد')} ({selectedSalesInvoices.length})
            </button>
          )}
        </div>
      )}

      {/* Enhanced Sales Invoice Filter */}
      <div className="inventory-controls">
        <div className="search-section">
          <input
            type="text"
            className="search-input"
            placeholder={`🔍 ${t('searchSalesInvoices', 'البحث في فواتير المبيعات (رقم الفاتورة، اسم الزبون)...')}`}
            value={salesInvoiceFilter}
            onChange={(e) => setSalesInvoiceFilter(e.target.value)}
          />
          {salesInvoiceFilter && (
            <button
              className="btn btn-secondary btn-xs"
              onClick={() => setSalesInvoiceFilter('')}
              title={t('clearFilters', 'مسح الفلاتر')}
            >
              🔄
            </button>
          )}
        </div>
      </div>

      <div className="table-container">
        <table className={`data-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`}>
          <thead>
            <tr>
              <th className="checkbox-column">
                <input
                  type="checkbox"
                  checked={filteredSalesInvoices.length > 0 && selectedSalesInvoices.length === filteredSalesInvoices.length}
                  onChange={toggleSelectAll}
                  title={t('selectAll', 'تحديد الكل')}
                />
              </th>
              <th>{t('invoiceNumber', 'رقم الفاتورة')}</th>
              <th>{t('date', 'التاريخ')}</th>
              <th>{t('customer', 'الزبون')}</th>
              <th>{t('paymentMethod', 'طريقة الدفع')}</th>
              <th>{t('amount', 'المبلغ')}</th>
              <th>{t('status', 'الحالة')}</th>
              <th>{t('actions', 'الإجراءات')}</th>
            </tr>
          </thead>
          <tbody>
            {filteredSalesInvoices.length === 0 ? (
              <tr>
                <td colSpan="8" style={{ textAlign: 'center', padding: '20px' }}>
                  {salesInvoiceFilter.trim()
                    ? `${t('noInvoicesMatchingFilter', 'لا توجد فواتير مطابقة للفلتر')}: "${salesInvoiceFilter}"`
                    : t('noInvoicesSaved', 'لا توجد فواتير محفوظة')
                  }
                </td>
              </tr>
            ) : (
              filteredSalesInvoices.map((invoice) => (
                <tr key={invoice.id} className={selectedSalesInvoices.includes(invoice.id) ? 'selected' : ''}>
                  <td className="checkbox-column">
                    <input
                      type="checkbox"
                      className="row-checkbox"
                      checked={selectedSalesInvoices.includes(invoice.id)}
                      onChange={() => toggleSelectItem(invoice.id)}
                    />
                  </td>
                  <td>{invoice.invoiceNumber}</td>
                  <td>{invoice.date}</td>
                  <td>{translateCustomerName(invoice.customerName)}</td>
                  <td>
                    <span className={`payment-method ${(invoice.paymentMethod === 'نقداً' || invoice.paymentMethod === 'Espèces' || invoice.paymentMethod === 'En espèces' || invoice.paymentMethod === 'Cash') ? 'cash' : 'credit'}`}>
                      {invoice.paymentMethod === 'نقداً' ? t('cash', 'نقداً') :
                       invoice.paymentMethod === 'دين' ? t('credit', 'دين') :
                       invoice.paymentMethod || t('cash', 'نقداً')}
                    </span>
                  </td>
                  <td>{formatPrice(invoice.finalTotal)}</td>
                  <td>
                    <span className={`status ${(invoice.paymentMethod === 'نقداً' || invoice.paymentMethod === 'Espèces' || invoice.paymentMethod === 'En espèces' || invoice.paymentMethod === 'Cash') ? 'paid' : 'pending'}`}>
                      {(invoice.paymentMethod === 'نقداً' || invoice.paymentMethod === 'Espèces' || invoice.paymentMethod === 'En espèces' || invoice.paymentMethod === 'Cash') ? t('paid', 'مدفوعة') : t('debt', 'دين')}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons-group">
                      <button
                        className="btn btn-info btn-xs"
                        onClick={() => viewInvoice(invoice)}
                        title={t('viewInvoiceDetails', 'عرض تفاصيل الفاتورة')}
                      >
                        👁️
                      </button>
                      <button
                        className="btn btn-secondary btn-xs"
                        onClick={() => printInvoice(invoice)}
                        title={t('normalPrint', 'طباعة عادية')}
                      >
                        🖨️
                      </button>
                      <button
                        className="btn btn-success btn-xs"
                        onClick={() => printThermalInvoice(invoice)}
                        title={t('thermalPrint', 'طباعة حرارية')}
                      >
                        🧾
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Invoice Details Modal */}
      {showInvoiceModal && selectedInvoice && (
        <div className="modal-overlay" onClick={() => setShowInvoiceModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{t('invoiceDetails', 'تفاصيل الفاتورة')} {selectedInvoice.invoiceNumber}</h3>
              <button className="modal-close" onClick={() => setShowInvoiceModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <div className="invoice-details">
                <p><strong>{t('date', 'التاريخ')}:</strong> {selectedInvoice.date}</p>
                <p><strong>{t('customer', 'الزبون')}:</strong> {translateCustomerName(selectedInvoice.customerName)}</p>
                <p><strong>{t('paymentMethod', 'طريقة الدفع')}:</strong> {selectedInvoice.paymentMethod}</p>
              </div>
              <table className="invoice-items-table">
                <thead>
                  <tr>
                    <th>{t('product', 'المنتج')}</th>
                    <th>{t('quantity', 'الكمية')}</th>
                    <th>{t('price', 'السعر')}</th>
                    <th>{t('total', 'المجموع')}</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedInvoice.items.map((item, index) => (
                    <tr key={index}>
                      <td>{item.productName}</td>
                      <td>{item.quantity}</td>
                      <td>{formatPrice(item.price)}</td>
                      <td>{formatPrice(item.total)}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="3"><strong>{t('finalTotal', 'المجموع النهائي')}</strong></td>
                    <td><strong>{formatPrice(selectedInvoice.finalTotal)}</strong></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalesManagement;
